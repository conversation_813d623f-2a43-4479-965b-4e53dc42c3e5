<?php

echo "🚀 Testing browser simulation for 24-HD.com...\n";

function simulate_browser_session($movie_url) {
    echo "🌐 Simulating browser session for: $movie_url\n";
    
    // Step 1: Get main page with cookies
    $cookie_jar = tempnam(sys_get_temp_dir(), 'cookies');
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $movie_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        CURLOPT_HTTPHEADER => [
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language: th-TH,th;q=0.9,en;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Connection: keep-alive',
            'Upgrade-Insecure-Requests: 1',
            'Sec-Fetch-Dest: document',
            'Sec-Fetch-Mode: navigate',
            'Sec-Fetch-Site: none',
            'Cache-Control: max-age=0'
        ],
        CURLOPT_COOKIEJAR => $cookie_jar,
        CURLOPT_COOKIEFILE => $cookie_jar,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_ENCODING => 'gzip, deflate, br'
    ]);
    
    $html = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    echo "   Step 1 - Main page: HTTP $http_code, " . strlen($html) . " chars\n";
    
    if ($http_code !== 200 || strpos($html, 'Cloudflare') !== false) {
        echo "   ❌ Failed to get main page or Cloudflare blocked\n";
        curl_close($ch);
        unlink($cookie_jar);
        return false;
    }
    
    // Extract data from HTML
    $post_id = null;
    $server = 1;
    $nonce = '';
    
    if (preg_match('/"post_id"\s*:\s*(\d+)/', $html, $matches)) {
        $post_id = $matches[1];
        echo "   Found post_id: $post_id\n";
    }
    
    if (preg_match('/ajax_player\s*=\s*\{[^}]*"nonce"\s*:\s*"([^"]*)"/', $html, $matches)) {
        $nonce = $matches[1];
        echo "   Found nonce: '$nonce' (length: " . strlen($nonce) . ")\n";
    }
    
    if (!$post_id) {
        echo "   ❌ Could not extract post_id\n";
        curl_close($ch);
        unlink($cookie_jar);
        return false;
    }
    
    // Step 2: Try API call with session cookies
    echo "\n   Step 2 - API call with cookies...\n";
    
    $api_url = 'https://api.24-hd.com/get.php';
    $form_data = "action=halim_ajax_player&nonce=$nonce&episode=1&server=$server&postid=$post_id&lang=&title=";
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $api_url,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $form_data,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: */*',
            'X-Requested-With: XMLHttpRequest',
            'Referer: ' . $movie_url,
            'Origin: https://www.24-hd.com'
        ]
    ]);
    
    $api_response = curl_exec($ch);
    $api_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    echo "   API response: HTTP $api_http_code, " . strlen($api_response) . " chars\n";
    
    if ($api_response && strpos($api_response, 'ไม่พบ รายการ') === false) {
        echo "   ✅ Success! API returned valid response\n";
        file_put_contents("browser_sim_response_{$post_id}.html", $api_response);
        echo "   Response saved to: browser_sim_response_{$post_id}.html\n";
        
        // Check for iframe
        if (preg_match('/<iframe[^>]+src=["\']*([^"\']+)["\']*[^>]*>/i', $api_response, $matches)) {
            echo "   🎬 Found iframe: {$matches[1]}\n";
            curl_close($ch);
            unlink($cookie_jar);
            return $matches[1];
        }
        
        echo "   Response preview: " . substr($api_response, 0, 200) . "...\n";
    } else {
        echo "   ❌ API call failed or returned 'not found'\n";
        echo "   Response preview: " . substr($api_response, 0, 200) . "...\n";
    }
    
    curl_close($ch);
    unlink($cookie_jar);
    return false;
}

// Test with different movies
$test_urls = [
    'https://www.24-hd.com/final-destination-4/',
    'https://www.24-hd.com/11-rebels/'
];

foreach ($test_urls as $url) {
    echo "\n" . str_repeat("=", 60) . "\n";
    $result = simulate_browser_session($url);
    if ($result) {
        echo "🎉 Successfully got player URL: $result\n";
        break;
    }
    echo "\n";
}

echo "\n✅ Browser simulation testing completed!\n";
?>
