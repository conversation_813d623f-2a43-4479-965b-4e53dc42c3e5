<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
	<script src="https://cdnjs.cloudflare.com/ajax/libs/mobile-detect/1.4.5/mobile-detect.min.js"></script>
    <meta charset="UTF-8">
	<meta name="referrer" content="no-referrer">	
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <title>All Player V 1.3.1</title>
</head>
<body>

   <style>
        body {
            margin-top: 0px;
            margin-bottom: 0px;
            background: rgb(0, 0, 0);;
        }

        .info {

            color: white;
            padding: 10px;
            position: fixed;
            top: 0px;
            left: 0px;
            pading: 0px;
            font-size: 0px;
        }

        .info .server, .info .total, .info .p2p, .info .connected-peers .info .ip-peers {
            display: none;
        }

        #video_container {
            max-width: 100vw;
            /*width: 100%;*/
            width: 100vw !important;
            height: 100vh !important;
        }

        #video {
            width: 100vw !important;
            height: 100vh !important;
            outline: none !important;
        }

        video, video:focus {
            outline: none !important;
        }

        #player-wrapper {
            position: relative;
        }

        #player-wrapper .qualities {
            position: absolute;
            left: 0px;
            top: 0px;
            padding: 1rem;
            text-align: right;
        }

        #player-wrapper .qualities img {
            padding: 1rem;
            cursor: pointer;
        }

        #player-wrapper .qualities ul {
            list-style: none;
            padding: 0px;
        }

        #player-wrapper .qualities ul li {
            padding: 1rem;
            color: white;
            background: transparent;
        }

        #player-wrapper .qualities ul li.active {
            color: green;
            background: gray;
        }

        #player-wrapper .qualities ul li:hover {
            background: red;
        }

        #allowDomain {
            display: none !important;
        }
    </style>
	
<div id="content"><script async src="p2p-media-loader-core_v1.js?v=1"></script>

<script>
    const URL_P2P_MEDIA_LOADER_HLSJS = "p2p-media-loader-hlsjs_v1.js?v=1";
</script>

<article>

<!-- Begin demo copy-paste -->
<div id="main-view">
    <div class="wrapper">
        <div class="column-1">
            <div id="video_container"></div>
            <div id="chart_container">
                <div id="legend"></div>
                <div id="legend-totals"></div>
                <div id="y_axis"></div>
                <div id="chart"></div>
            </div>
        </div>
        <div id="graphd" class="column-2">
            <form id="videoUrlForm">
            </form>
            <div id="graph"></div>
            <h3>Trackers:</h3>
            <div id="announce"></div>
        </div>
    </div>
</div>

<script>

    var getUrlParameter = function getUrlParameter(sParam) {
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');

            if (sParameterName[0] === sParam) {
                return sParameterName[1] === undefined ? true : sParameterName[1];
            }
        }
    };

    if (typeof Object.assign != 'function') {
        Object.assign = function (target) {
            'use strict';
            if (target == null) {
                throw new TypeError('Cannot convert undefined or null to object');
            }

            target = Object(target);
            for (var index = 1; index < arguments.length; index++) {
                var source = arguments[index];
                if (source != null) {
                    for (var key in source) {
                        if (Object.prototype.hasOwnProperty.call(source, key)) {
                            target[key] = source[key];
                        }
                    }
                }
            }
            return target;
        };
    }
    var extractHostname = function (url) {
        var hostname;
        //find & remove protocol (http, ftp, etc.) and get hostname

        if (url.indexOf("//") > -1) {
            hostname = url.split('/')[2];
        }
        else {
            hostname = url.split('/')[0];
        }

        //find & remove port number
        hostname = hostname.split(':')[0];
        //find & remove "?"
        hostname = hostname.split('?')[0];
        if (url.indexOf('https://') >= 0) {
            return 'https://' + hostname;
        } else {
            return 'http://' + hostname;
        }

    }	
var id = getUrlParameter('id');
var ptype = getUrlParameter('ptype');
//var start_posi = -1;
//var backup = 0;
var start_posi = getUrlParameter('position');
var backup = getUrlParameter('backup');
var jwstart = 0;


	
var md = new MobileDetect(window.navigator.userAgent);
// Opera 8.0+
var isOpera = (!!window.opr && !!opr.addons) || !!window.opera || navigator.userAgent.indexOf(' OPR/') >= 0;
// Firefox 1.0+
var isFirefox = typeof InstallTrigger !== 'undefined';
// Safari 3.0+ "[object HTMLElementConstructor]" 
var isSafari = /constructor/i.test(window.HTMLElement) || (function (p) { return p.toString() === "[object SafariRemoteNotification]"; })(!window['safari'] || (typeof safari !== 'undefined' && window['safari'].pushNotification));
// Internet Explorer 6-11
var isIE = /*@cc_on!@*/false || !!document.documentMode;
// Edge 20+
var isEdge = !isIE && !!window.StyleMedia;
// Chrome 1 - 79
var isChrome = !!window.chrome && (!!window.chrome.webstore || !!window.chrome.runtime);

var p2pactive = true;
var debugenable = false;
var consumeOnly = false;
var maxsend = 300;



if (!debugenable) {
	 document.getElementById("chart_container").style.display = "none";
	 document.getElementById("graphd").style.display = "none";
}

//if (md.userAgent() == null)
//	p2pactive = true;
//else
//	p2pactive = false;
	
if (md.os() == 'AndroidOS' ||  md.is('iPad') || isOpera || isFirefox || isIE || isEdge || isChrome || isSafari)
	p2pactive = true;	

//if (md.mobile() /*|| md.tablet()*/)
//	consumeOnly = false;

var p2pdisable = false;	

	
if (md.tablet())	
	p2pactive = true;	
	
if (md.mobile()) {
	maxsend = 100;
}

if (/*md.os() == 'webOS' || */ navigator.userAgent.search(/(lg|LG|sony|samsung|SONY|TV|SmartTV|SMART-TV|Tizen(.*TV))/i) >= 0) {
	p2pactive = false;
	p2pdisable = true;
}

	
    function waitForGlobalObject(objectName, objectNextName) {
        return new Promise((resolve) => {
            function check() {
                if ((window[objectName] !== undefined)
                        && ((objectNextName === undefined) || window[objectName][objectNextName] !== undefined)) {
                    resolve();
                } else {
                    setTimeout(check, 200);
                }
            }

            check();
        });
    }

    function waitForModule(moduleName) {
        return new Promise((resolve) => {
            function check() {
                try {
                    resolve(require(moduleName));
                } catch (e) {
                    setTimeout(check, 200);
                }
            }

            check();
        });
    }

    function loadScript(src) {
        return new Promise((resolve, reject) => {
            const script= document.createElement('script');
            script.type= 'text/javascript';
            script.onload = () => {
                resolve();
            };
            script.onerror = () => {
                console.log("Failed to load script", src);
                reject();
            };
            script.src = src;
            document.head.appendChild(script);
        });
    }
	


    function loadStyle(src) {
        return new Promise((resolve, reject) => {
            const link= document.createElement('link');
            link.rel = 'stylesheet';
            link.type= 'text/css';
            link.onload = () => {
                resolve();
            };
            link.onerror = () => {
                console.log("Failed to load CSS", src);
                reject();
            };
            link.href = src;
            document.head.appendChild(link);
        });
    }

    class player123 {
        async init() {
            await waitForGlobalObject("p2pml", "core");

            this.isP2PSupported = p2pml.core.HybridLoader.isSupported();
            if (!this.isP2PSupported) {
                document.querySelector("#error-webrtc-data-channels").classList.remove("hide");
            }



            this.liveSyncDurationCount = 7;

            this.initForm();


			if (backup == 1) {
				this.videoUrl = '/newplaylist_g/' + id + '/' + id + '.m3u8';
			} else {
				if (ptype == 2) {
					this.videoUrl = '/newplaylist/' + id + '/' + id + '.m3u8';
				} else {
					this.videoUrl = '/newplaylist/' + id + '/' + id + '.m3u8';
				}
			}
			



            this.videoContainer = document.getElementById("video_container");

            this.loadSpeedTimespan = 10; // seconds

			if (p2pactive && debugenable) {
				const P2PGraphClass = await waitForModule("p2p-graph");
				this.graph = new P2PGraphClass("#graph");
				this.graph.add({ id: "me", name: "You", me: true });
				await waitForGlobalObject("Rickshaw");
				this.initChart();
			}
            this.Restart_Player();
			
        }

        initForm() {
			if (!p2pactive && debugenable)
				return;
            var form = document.getElementById("videoUrlForm");
            var params = new URLSearchParams(document.location.search);

            var value = params.get("url");
            if (value) {
                form.url.value = value;
            }

            value = params.get("type");
            if (value) {
                form.type.value = value;
            }

            value = params.get("swarm");
            if (value) {
                this.swarmId = value;
            }

            //value = params.get("trackers");
			value = "wss://tracker.77player.xyz"
            if (value) {
                this.trackers = value.split(",");
            }
        }

        async Restart_Player() { 
            this.downloadStats = [];
            this.downloadTotals = { http: 0, p2p: 0 };
            this.uploadStats = [];
            this.uploadTotal = 0;

            while (this.videoContainer.hasChildNodes()) {
                this.videoContainer.removeChild(this.videoContainer.lastChild);
            }
			var retfetch = "";
			var prefox_seed = 0;
			await fetch(this.videoUrl+"")
			.then(response => {
				return response.text();
			})
			.then(data => retfetch = data);
			var position = retfetch.search(/2560x1440/i);
			if (position > 0) {
				prefox_seed =14;
			} else { //480x360
				if (retfetch.search(/1920x1080/i) > 0 && retfetch.search(/480x360/i) > 0)
					prefox_seed =8;
				else
					prefox_seed =1;
			}
			var now = new Date();
			var start = new Date(now.getFullYear(), 0, 0);
			var diff = now - start;
			var oneDay = 1000 * 60 * 60 * 24;
			var day = Math.floor(diff / oneDay);			
			//alert( prefox_seed+id);
            const config = {
                segments: {
                    swarmId: prefox_seed+id
                },
                loader: {
					httpFailedSegmentTimeout: 10000,
					consumeOnly: consumeOnly,
					httpDownloadMaxPriority: 8,
					cachedSegmentsCount: maxsend,
					requiredSegmentsPriority: 3,
					simultaneousP2PDownloads: 8,
					simultaneousHttpDownloads: 8,
					httpDownloadProbabilitySkipIfNoPeers: false,
					httpDownloadProbabilityInterval: 1000,
					httpDownloadInitialTimeout: 100,
					
					rtcConfig: {
						  iceServers: [
							{ urls: "stun:stun.l.google.com:19302" },
							{ urls: "stun:global.stun.twilio.com:3478" }
						  ]
					}	
                }
            };
			
			
            if (this.trackers) {
				var tracker_index = id.substr(0,1);
				tracker_index = parseInt(tracker_index, 16)%4;
				if (tracker_index == 0)	{
					config.loader.trackerAnnounce = [
						  "wss://t2.24playerhd.com",
					];
				} else if (tracker_index == 1)	{
					config.loader.trackerAnnounce = [
						  "wss://t1.24playerhd.com",
					];
				} else if (tracker_index == 2)	{
					config.loader.trackerAnnounce = [
						  "wss://t4.24playerhd.com",
					];
				} else {
					config.loader.trackerAnnounce = [
						  "wss://t3.24playerhd.com",
					];
				}		
            }
			if (p2pdisable != true ) {
				await loadScript("hlsv1.js?v=1.2"); 
				//await loadScript("https://cdn.jsdelivr.net/npm/hls.js@0.14.17");
				
				/*if (!Hls.isSupported()) {
					document.querySelector("#error-hls-js").classList.remove("hide");
				}*/
													
				await loadScript(URL_P2P_MEDIA_LOADER_HLSJS);
				this.engine = this.isP2PSupported ? new p2pml.hlsjs.Engine(config) : undefined;

				this.initJwPlayer();

				if (!p2pactive && debugenable) {
					document.getElementById("announce").innerHTML = " Disable <br />";
					return;
				}

				if (this.isP2PSupported) {
					this.engine.on(p2pml.core.Events.PieceBytesDownloaded, this.onBytesDownloaded.bind(this));
					this.engine.on(p2pml.core.Events.PieceBytesUploaded, this.onBytesUploaded.bind(this));

					var trackerAnnounce = this.engine.getSettings().loader.trackerAnnounce;
					if (Array.isArray(trackerAnnounce)) {
						document.getElementById("announce").innerHTML = " Enable <br />" + trackerAnnounce.join(" <br />");
					}
				}
				if (debugenable) {
					this.refreshChart();
					this.refreshGraph();
				}
			} else {
				this.initJwPlayer();
			}
        }
		


        async initJwPlayer() {
            var video = document.createElement("div");
            video.id = "video";
            video.volume = 0;
            video.setAttribute("playsinline", "");
            video.setAttribute("muted", "");
            video.setAttribute("autoplay", "");
            this.videoContainer.appendChild(video);

			if(!md.is('iPad') && md.mobile() ) {
				await loadScript("https://content.jwplatform.com/libraries/foHt6P0J.js")
				//await loadScript("https://cdn.jsdelivr.net/npm/@hola.org/jwplayer-hlsjs@latest/dist/jwplayer.hlsjs.min.js")
				await loadScript("jwplayer.hlsjs.min.js")
			} else if(!md.is('iPad') ) {
				//await loadScript("https://api2.imoviehds.com/8.8.2/jwplayer.js?ver=9") 
				await loadScript("8.8.2/jwplayer.js?ver=9")
				await loadScript("jwplayer.hlsjs.min.js")
			} else {
                await loadScript("https://content.jwplatform.com/libraries/foHt6P0J.js")
				await loadScript("jwplayer.hlsjs.min.js")
			}

			var vtt = "https://data.24playerhd.com/vtt/"+id+"/"+id+".vtt";
			if (md.mobile()) {
				vtt = "";
			}			
            var player = jwplayer("video");
			var json = {
				"sources": [
					{
						"type": "hls",
						"file": this.videoUrl
					}
				],
				"playbackRateControls": true,
				"mute": false,
				"autostart": "false",
				"preload": "none",
				"cast": {"appid": "00000000"},
				"base": ".",
				"volume": 100,
				"key": "ITWMv7t88JGzI0xPwW8I0+LveiXX9SWbfdmt0ArUSyc=",
				androidhls: true,

			}			
            player.setup(json);
			if (p2pdisable != true ) {
				jwplayer_hls_provider.attach();	
			}
			
			
			xaddbutton2(player,id,json);
			
			if (0 /*p2pactive == false || md.mobile()*/) { //Set Level
				player.on('play', function(e) {
					if (!jwstart) {
						jwplayer("video").setCurrentQuality(1);
						jwstart = 1;
					}
					if (start_posi >= 0)
						player.seek(start_posi);
					
				});
					xaddbutton2(player,id,json);
			} else {
				player.on('play', function(e) {
					if (start_posi >= 0)
						player.seek(start_posi);
				});				
			}
			
		player.on('levelsChanged',function () {
			//console.log("Change" + player.getQualityLevels()[player.getCurrentQuality()].height);

		});			

			
			if (start_posi >= 0) {
				//player.play();
			} else {
			
			}

            if (this.isP2PSupported && p2pactive == true) {
                p2pml.hlsjs.initJwPlayer(player, {
                    liveSyncDurationCount: this.liveSyncDurationCount,
                    loader: this.engine.createLoaderClass()
                });
            }
        }
		
	
		
        initChart() {
            var chartConf = {
                element: document.querySelector("#chart"),
                renderer: 'multi',
                interpolation: "basis",
                stack: false,
                min: 'auto',
                strokeWidth: 1,
                series: [
                    {name: "Upload P2P", color: "#88eab9", data: [], renderer: 'area'},
                    {name: " - P2P", color: "#88b9ea", data: [], renderer: 'area'},
                    {name: " - HTTP", color: "#eae288", data: [], renderer: 'area'},
                    {name: "Download", color: "#f64", data: [], renderer: 'line'}
                ]
            };

            this.chart = new Rickshaw.Graph(chartConf);

            new Rickshaw.Graph.Axis.X({
                graph: this.chart,
                tickFormat: () => ''
            });

            new Rickshaw.Graph.Axis.Y({
                graph: this.chart,
                orientation: 'left',
                element: document.getElementById('y_axis')
            });

            this.legend = new Rickshaw.Graph.Legend({
                graph: this.chart,
                element: document.getElementById('legend')
            });

            this.legendTotals = new Rickshaw.Graph.Legend({
                graph: this.chart,
                element: document.getElementById("legend-totals")
            });

            this.chart.render();
            setInterval(this.updateChartData.bind(this), 500);

            var chartResize = () => {
                chartConf.width = this.chart.element.clientWidth;
                this.chart.configure(chartConf);
                this.chart.render();
            };

            chartResize();
            window.addEventListener("resize", chartResize);
        }

        refreshChart() {
            if (!this.chart) {
                return;
            }

            var data0 = this.chart.series[0].data;
            var data1 = this.chart.series[1].data;
            var data2 = this.chart.series[2].data;
            var data3 = this.chart.series[3].data;
            var lastX = data0.length > 0 ? data0[data0.length - 1].x : -1;

            var seriesDataMapper = (currentValue, index) => ({x: index + lastX + 1, y: 0});

            data0.length = 0;
            data1.length = 0;
            data2.length = 0;
            data3.length = 0;

            var stubData = Array.apply(null, Array(200)).map(seriesDataMapper);
            data0.push.apply(data0, stubData.slice(0));
            data1.push.apply(data1, stubData.slice(0));
            data2.push.apply(data2, stubData.slice(0));
            data3.push.apply(data3, stubData.slice(0));

            this.chart.update();
        }

        updateChartData() {
            var downloadSpeed = this.getDownloadSpeed();
            var http = Number((downloadSpeed.http * 8 / 1000000).toFixed(2));
            var p2p = Number((downloadSpeed.p2p * 8 / 1000000).toFixed(2));
            var total = Number((http + p2p).toFixed(2));
            var upload = Number(this.getUploadSpeed() * 8 / 1000000).toFixed(2);

            var data0 = this.chart.series[0].data;
            var data1 = this.chart.series[1].data;
            var data2 = this.chart.series[2].data;
            var data3 = this.chart.series[3].data;
            var x = data0.length > 0 ? data0[data0.length - 1].x + 1 : 0;

            data0.shift();
            data1.shift();
            data2.shift();
            data3.shift();
            data0.push({x: x, y: -upload});
            data1.push({x: x, y: total});
            data2.push({x: x, y: http});
            data3.push({x: x, y: total});
            this.chart.update();

            this.formatChartLegendLine(0, total);
            this.formatChartLegendLine(1, http);
            this.formatChartLegendLine(2, p2p);
            this.formatChartLegendLine(3, upload);

            this.updateLegendTotals();
        }

        formatChartLegendLine(index, speed) {
            if (this.legend) {
                var line = this.legend.lines[index];
                line.element.childNodes[1].textContent = line.series.name + ' - ' + speed + ' Mbit/s';
            }
        }

        updateLegendTotals() {
            if (!this.legendTotals) {
                return;
            }

            var httpMb = this.downloadTotals.http / 1048576;
            var p2pMb = this.downloadTotals.p2p / 1048576;
            var totalMb = httpMb + p2pMb;
            var uploadMb = this.uploadTotal / 1048576;

            if (totalMb != 0) {
                this.legendTotals.lines[0].element.childNodes[1].textContent
                    = "Download - "
                    + Number(totalMb).toFixed(1) + " MiB";

                this.legendTotals.lines[1].element.childNodes[1].textContent
                    = " - HTTP - "
                    + Number(httpMb).toFixed(1) + " MiB - "
                    + Number((httpMb * 100) / totalMb).toFixed(0) + "%";

                this.legendTotals.lines[2].element.childNodes[1].textContent
                    = " - P2P - "
                    + Number(p2pMb).toFixed(1) + " MiB - "
                    + Number((p2pMb * 100) / totalMb).toFixed(0) + "%";

                this.legendTotals.lines[3].element.childNodes[1].textContent
                    = "Upload P2P - "
                    + Number(uploadMb).toFixed(1) + " MiB";
            }
        }

        getDownloadSpeed() {
            var startingPoint = performance.now() - (this.loadSpeedTimespan * 1000);
            var httpSize = 0;
            var p2pSize = 0;

            var i = this.downloadStats.length;
            while (i--) {
                var stat = this.downloadStats[i];
                if (stat.timestamp < startingPoint) {
                    break;
                }

                if (stat.method === "p2p") {
                    p2pSize += stat.size;
                } else if (stat.method === "http") {
                    httpSize += stat.size;
                }
            }

            this.downloadStats.splice(0, i + 1);

            return {p2p: p2pSize / this.loadSpeedTimespan, http: httpSize / this.loadSpeedTimespan};
        }

        getUploadSpeed() {
            var startingPoint = performance.now() - (this.loadSpeedTimespan * 1000);
            var size = 0;

            var i = this.uploadStats.length;
            while (i--) {
                var stat = this.uploadStats[i];
                if (stat.timestamp < startingPoint) {
                    break;
                }

                size += stat.size;
            }

            this.uploadStats.splice(0, i + 1);

            return size / this.loadSpeedTimespan;
        }

        onBytesDownloaded(method, size) {
            this.downloadStats.push({method: method, size: size, timestamp: performance.now()});
            this.downloadTotals[method] += size;
        }

        onBytesUploaded(method, size) {
            this.uploadStats.push({size: size, timestamp: performance.now()});
            this.uploadTotal += size;
        }

        refreshGraph(p2pLoader) {
            if (!this.graph) {
                return;
            }

            var nodes = this.graph.list();
            for (var i = 0; i < nodes.length; i++) {
                if (nodes[i].id !== "me") {
                    this.graph.disconnect("me", nodes[i].id);
                    this.graph.remove(nodes[i].id);
                }
            }

            if (this.isP2PSupported) {
                this.engine.on(p2pml.core.Events.PeerConnect, this.onPeerConnect.bind(this));
                this.engine.on(p2pml.core.Events.PeerClose, this.onPeerClose.bind(this));
            }
        }

        onPeerConnect(peer) {
            if (!this.graph.hasPeer(peer.id)) {
                this.graph.add({id: peer.id, name: peer.remoteAddress || 'Unknown'});
                this.graph.connect("me", peer.id);
            }
        }

        onPeerClose(id) {
            if (this.graph.hasPeer(id)) {
                this.graph.disconnect("me", id);
                this.graph.remove(id);
            }
        }

        constructor() {
            this.hlsLevelSwitcher = {
                auto: "Auto",
                hls: undefined,
                select: undefined,

                init: function (hls, select) {
                    if (hls.levelController.levels.length < 2) {
                        select.style.display = "none";
                        return;
                    } else {
                        select.style.display = "block";
                    }

                    this.hls = hls;
                    this.select = select;

                    this._clearOptions();
                    this._addOption(this.auto);
                    hls.levelController.levels.forEach((e, i) => {
                        var name = [];
                        if (e.height) {
                            name.push(e.height + "p");
                        }
                        if (e.bitrate) {
                            name.push(Math.round(e.bitrate / 1000) + "k");
                        }
                        if (name.length === 0) {
                            name.push("Quality #" + i);
                        }
                        this._addOption(name.join(" / "), i);
                    });

                    hls.on(Hls.Events.LEVEL_SWITCHED, this._hlsLevelSwitch.bind(this));

                    this.select.addEventListener("change", (event) => {
                        hls.nextLevel = event.target.selectedIndex - 1;
                        this._hlsLevelSwitch();
                    });
                },

                _hlsLevelSwitch: function () {
                    var auto = this.select.querySelector("option:not([data-index])");
                    var curr = this.select.querySelector("option[data-index='" + this.hls.currentLevel + "']");
                    if (this.hls.autoLevelEnabled || this.hls.currentLevel === -1) {
                        auto.selected = true;
                        auto.text = curr ? curr.text + " (" + this.auto + ")" : this.auto;
                    } else {
                        curr.selected = true;
                        auto.text = this.auto;
                    }
                },

                _clearOptions: function () {
                    while (this.select.options.length) {
                        this.select.remove(0);
                    }
                },

                _addOption: function (text, index) {
                    var option = document.createElement("option");
                    option.text = text;
                    if (index !== undefined) {
                        option.dataset.index = index;
                    }
                    this.select.add(option);
                }
            };

            this.shakaLevelSwitcher = {
                auto: "Auto",
                player: undefined,
                select: undefined,

                init: function (player, select) {
                    this.player = player;
                    this.select = select;

                    player.addEventListener("trackschanged", () => {
                        this._clearOptions();
                        this._addOption(this.auto);
                        this.player.getVariantTracks().forEach((e, i) => {
                            var name = [];

                            if (e.height) {
                                name.push(e.height + "p");
                            }

                            if (e.bandwidth) {
                                name.push(Math.round(e.bandwidth / 1000) + "k");
                            }

                            if (e.label) {
                                name.push(e.label);
                            } else if (e.language) {
                                name.push(e.language);
                            }

                            if (name.length === 0) {
                                name.push("Variant #" + i);
                            }

                            this._addOption(name.join(" / "), e.id);
                        });
                    });

                    player.addEventListener("adaptation", () => {
                        var variantId = this.player.getVariantTracks().find(i => i.active === true).id;
                        var curr = this.select.querySelector("option[data-variant-id='" + variantId + "']");
                        var auto = this.select.querySelector("option:not([data-variant-id])");
                        auto.text = curr ? curr.text + " (" + this.auto + ")" : this.auto;
                    });

                    select.addEventListener("change", () => {
                        var variantId = this.select.selectedOptions[ 0 ].dataset.variantId;
                        if (variantId) {
                            var variant = this.player.getVariantTracks().find(i => i.id == variantId);
                            this.player.configure({ abr: { enabled: false } });
                            this.player.selectVariantTrack(variant);
                            var auto = this.select.querySelector("option:not([data-variant-id])");
                            auto.text = this.auto;
                        } else {
                            this.player.configure({ abr: { enabled: true } });
                        }
                    });
                },

                _clearOptions: function () {
                    while (this.select.options.length) {
                        this.select.remove(0);
                    }
                },

                _addOption: function (text, variantId) {
                    var option = document.createElement("option");
                    option.text = text;
                    if (variantId) {
                        option.dataset.variantId = variantId;
                    }
                    this.select.add(option);
                }
            };
        }
    }

    const P2PPlayer = new player123();
    P2PPlayer.init();
	
	function	xaddbutton2(player2,id,json) {
      player2.addButton("/icon/next.svg", "forward 30 sec", function () {
        player2.seek(player2.getPosition() + 30);
      }, "forward");
      /*player2.addButton("/icon/back.svg", "backward 10 sec", function () {
        player2.seek(player2.getPosition() - 10);
      }, "backward");*/
			return;
	}	
	
   function sleep(milliseconds) {  
      return new Promise(resolve => setTimeout(resolve, milliseconds));  
   }
</script>
  </div>
</article>
</div>
</body>
</html>