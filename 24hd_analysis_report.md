# 24-HD.com Analysis Report

## การวิเคราะห์เว็บไซต์ 24-hd.com

### ข้อมูลที่ดึงได้สำเร็จ

จากการวิเคราะห์ URL: https://www.24-hd.com/final-destination-4/

✅ **ข้อมูลที่ดึงได้แล้ว:**
- **Title**: Final Destination 4 (2009) ไฟนอล เดสติเนชั่น 4 โกงตาย ทะลุตาย
- **IMDb Rating**: 5.1
- **YouTube ID**: yZFSkHTfH9Q
- **Poster Image**: http://www.24-hdd.com/wp-content/uploads/2024/02/Final-Destination-4-2009-ไฟนอล-เดสติเนชั่น-4-โกงตาย-ทะลุตาย.jpg

### Patterns ที่ใช้งานได้

#### 1. Title Extraction
```regex
/<h1[^>]*>([^<]+)<\/h1>/i
/<meta[^>]*property=["']og:title["'][^>]*content=["']([^"']+)["'][^>]*>/i
```

#### 2. IMDb Rating Extraction
```regex
/<span[^>]*class=["'][^"']*score[^"']*["'][^>]*>[\s\S]*?<\/noscript>([0-9]+\.?[0-9]*)/i
```

#### 3. YouTube ID Extraction
```regex
/videoId:\s*['"]([a-zA-Z0-9_-]+)['"]/
/youtube\.com\/vi\/([a-zA-Z0-9_-]+)/g
```

#### 4. Poster Image Extraction
```regex
/<meta[^>]*property=["']og:image["'][^>]*content=["']([^"']+)["'][^>]*>/i
```

### ปัญหาที่พบ

❌ **ปัญหาหลัก:**
1. **Cloudflare Protection**: เว็บไซต์มีการป้องกัน Cloudflare ที่ block การเข้าถึง AJAX
2. **Dynamic Player Loading**: Player ไม่ได้ embed แบบ static แต่ใช้ AJAX loading
3. **AJAX Player Detection**: ระบบใช้ `data-post-id` และ `data-server` เพื่อโหลด player

### โครงสร้าง Player System

เว็บไซต์ใช้ระบบ AJAX เพื่อโหลด player:

```html
<span class="halim-btn halim-btn-2 active halim-info-2-1 box-shadow" 
      data-post-id="29814" 
      data-server="1" 
      data-episode="1" 
      data-embed="0" 
      data-type="none">
  <i class="hl-server"></i> ตัวเล่นหลัก
</span>
```

**AJAX Request:**
- URL: `https://www.24-hd.com/wp-admin/admin-ajax.php`
- Method: POST
- Data: `action=doo_player_ajax&post=29814&nume=1&type=movie`

### การแก้ไขที่ทำแล้ว

#### 1. อัปเดต BaseScraper.php
- ✅ เพิ่ม pattern สำหรับ YouTube ID: `videoId:` และ `youtube.com/vi/`
- ✅ เพิ่ม pattern สำหรับ IMDb rating: `<span class="score">` with `</noscript>`
- ✅ ปรับปรุง title extraction ให้กรองข้อความที่ไม่ต้องการ

#### 2. อัปเดต HD24MovieScraper
- ✅ เพิ่ม method `extract_24hd_ajax_player()` สำหรับดึง player ผ่าน AJAX
- ✅ เพิ่ม method `fetch_ajax_player()` สำหรับจัดการ AJAX request
- ✅ ปรับปรุง `extract_24hd_main_player()` ให้ fallback ไป AJAX method

### แนวทางแก้ไขปัญหา Cloudflare

#### วิธีที่ 1: ใช้ cURL แทน file_get_contents
```php
protected function fetch_ajax_with_curl($ajax_url, $form_data, $referer) {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $ajax_url,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $form_data,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/x-www-form-urlencoded',
            'X-Requested-With: XMLHttpRequest',
            'Accept: application/json, text/javascript, */*; q=0.01',
            'Referer: ' . $referer,
            'Origin: https://www.24-hd.com'
        ]
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return ($http_code === 200) ? $response : false;
}
```

#### วิธีที่ 2: ใช้ Browser Automation (Selenium/Puppeteer)
- ใช้ headless browser เพื่อจำลองการใช้งานจริง
- สามารถผ่าน Cloudflare protection ได้

#### วิธีที่ 3: ใช้ Proxy/VPN Services
- ใช้ proxy services ที่มี IP rotation
- ใช้ residential proxy เพื่อหลีกเลี่ยง detection

### ข้อมูลเพิ่มเติมที่ต้องการ

🔍 **ยังต้องดึงข้อมูล:**
1. **Main Player URL**: จาก AJAX response
2. **M3U8 Files**: จาก player page
3. **Duration**: จากการคำนวณ M3U8 segments
4. **Thai Dubbed/Subbed**: แยกประเภทเสียง

### ขั้นตอนถัดไป

1. **ทดสอบ AJAX method** ที่อัปเดตแล้ว
2. **เพิ่ม error handling** สำหรับ Cloudflare protection
3. **ทดสอบการดึง M3U8 files** จาก player URL
4. **เพิ่มการคำนวณ duration** จาก M3U8 content
5. **ทดสอบกับหนังเรื่องอื่นๆ** เพื่อยืนยันความถูกต้อง

### สรุป

การวิเคราะห์เว็บไซต์ 24-hd.com สำเร็จในการดึงข้อมูล metadata หลัก (title, IMDb rating, YouTube ID, poster) แต่ยังมีปัญหาในการดึง player URL เนื่องจาก Cloudflare protection และระบบ AJAX loading

ระบบ scraper ได้รับการอัปเดตให้รองรับ patterns ใหม่และมี fallback mechanism สำหรับการดึง player ผ่าน AJAX แล้ว
