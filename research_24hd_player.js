const fs = require('fs');
const https = require('https');
const http = require('http');
const { URL } = require('url');
const { exec } = require('child_process');

class HD24PlayerResearcher {
    constructor() {
        this.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
        this.outputDir = './player_research';
        this.ensureOutputDir();
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    async fetchWithCurl(url) {
        return new Promise((resolve, reject) => {
            const curlCommand = `curl -s -L -H "User-Agent: ${this.userAgent}" -H "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" -H "Referer: https://www.24-hd.com/" "${url}"`;
            
            exec(curlCommand, { maxBuffer: 1024 * 1024 * 10 }, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                    return;
                }
                
                resolve({
                    statusCode: 200,
                    body: stdout
                });
            });
        });
    }

    saveToFile(filename, content) {
        const filePath = `${this.outputDir}/${filename}`;
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`💾 Saved: ${filePath} (${content.length} chars)`);
        return filePath;
    }

    async analyzePlayerURL(playerUrl) {
        console.log(`🔍 Analyzing player URL: ${playerUrl}`);

        try {
            const response = await this.fetchWithCurl(playerUrl);

            if (!response.body || response.body.length < 1000) {
                console.log(`❌ Invalid response from player URL`);
                return null;
            }

            console.log(`✅ Player page fetched: ${response.body.length} characters`);
            this.saveToFile('player_page.html', response.body);

            // Extract parameters from URL
            const urlObj = new URL(playerUrl);
            const id = urlObj.searchParams.get('id');
            const backup = urlObj.searchParams.get('backup') || '0';
            const ptype = urlObj.searchParams.get('ptype') || '1';

            console.log(`📋 Player parameters: id=${id}, backup=${backup}, ptype=${ptype}`);

            if (!id) {
                console.log(`❌ No ID parameter found in URL`);
                return null;
            }

            // Generate M3U8 URLs based on the JavaScript logic from player page
            const baseUrl = `https://main.24playerhd.com`;
            const m3u8Urls = [];

            if (backup == '1') {
                m3u8Urls.push(`${baseUrl}/newplaylist_g/${id}/${id}.m3u8`);
                console.log(`🔄 Using backup path (backup=1)`);
            } else {
                if (ptype == '2') {
                    m3u8Urls.push(`${baseUrl}/newplaylist/${id}/${id}.m3u8`);
                    console.log(`📺 Using ptype=2 path`);
                } else {
                    m3u8Urls.push(`${baseUrl}/newplaylist/${id}/${id}.m3u8`);
                    console.log(`📺 Using default path (ptype=1)`);
                }
            }

            // Also try backup path as fallback
            if (backup != '1') {
                m3u8Urls.push(`${baseUrl}/newplaylist_g/${id}/${id}.m3u8`);
                console.log(`🔄 Adding backup path as fallback`);
            }

            console.log(`📺 Generated M3U8 URLs:`);
            for (const url of m3u8Urls) {
                console.log(`   - ${url}`);
                await this.analyzeM3U8(url);
            }

            return {
                id,
                backup,
                ptype,
                m3u8Urls,
                playerContent: response.body
            };

        } catch (error) {
            console.error(`❌ Error analyzing player: ${error.message}`);
            return null;
        }
    }

    extractM3U8URLs(html) {
        const patterns = [
            /file:\s*["']([^"']+\.m3u8[^"']*)/gi,
            /source:\s*["']([^"']+\.m3u8[^"']*)/gi,
            /src:\s*["']([^"']+\.m3u8[^"']*)/gi,
            /url:\s*["']([^"']+\.m3u8[^"']*)/gi,
            /["']([^"']*\.m3u8[^"']*)/gi,
            /https?:\/\/[^\s"']+\.m3u8[^\s"']*/gi
        ];

        const urls = new Set();
        
        for (const pattern of patterns) {
            let match;
            while ((match = pattern.exec(html)) !== null) {
                const url = match[1] || match[0];
                if (url && url.includes('.m3u8')) {
                    urls.add(url);
                }
            }
        }
        
        return Array.from(urls);
    }

    extractVideoSources(html) {
        const patterns = [
            /<video[^>]*>[\s\S]*?<\/video>/gi,
            /<source[^>]+src=["']([^"']+)["'][^>]*>/gi,
            /jwplayer[^}]*file:\s*["']([^"']+)["']/gi,
            /videojs[^}]*src:\s*["']([^"']+)["']/gi
        ];

        const sources = new Set();
        
        for (const pattern of patterns) {
            let match;
            while ((match = pattern.exec(html)) !== null) {
                if (match[1]) {
                    sources.add(match[1]);
                }
            }
        }
        
        return Array.from(sources);
    }

    async analyzeM3U8(m3u8Url) {
        console.log(`📺 Analyzing M3U8: ${m3u8Url}`);
        
        try {
            const response = await this.fetchWithCurl(m3u8Url);
            
            if (!response.body) {
                console.log(`❌ Failed to fetch M3U8`);
                return null;
            }
            
            const filename = `m3u8_${Date.now()}.m3u8`;
            this.saveToFile(filename, response.body);
            
            const analysis = this.parseM3U8Content(response.body);
            console.log(`   Type: ${analysis.type}`);
            console.log(`   Duration: ${analysis.duration} minutes`);
            console.log(`   Segments: ${analysis.segments}`);
            
            if (analysis.type === 'master') {
                console.log(`   Resolutions: ${analysis.resolutions.join(', ')}`);
                
                // Analyze first resolution playlist
                if (analysis.playlists.length > 0) {
                    const firstPlaylist = analysis.playlists[0];
                    console.log(`   Analyzing resolution playlist: ${firstPlaylist}`);
                    await this.analyzeM3U8(firstPlaylist);
                }
            }
            
            return analysis;
            
        } catch (error) {
            console.error(`❌ Error analyzing M3U8: ${error.message}`);
            return null;
        }
    }

    parseM3U8Content(content) {
        const lines = content.split('\n').map(line => line.trim()).filter(line => line);
        
        const analysis = {
            type: 'unknown',
            duration: 0,
            segments: 0,
            resolutions: [],
            playlists: []
        };
        
        // Check if it's a master playlist
        if (content.includes('#EXT-X-STREAM-INF:')) {
            analysis.type = 'master';
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                
                if (line.startsWith('#EXT-X-STREAM-INF:')) {
                    // Extract resolution
                    const resMatch = line.match(/RESOLUTION=(\d+x\d+)/);
                    if (resMatch) {
                        analysis.resolutions.push(resMatch[1]);
                    }
                    
                    // Next line should be the playlist URL
                    if (i + 1 < lines.length && !lines[i + 1].startsWith('#')) {
                        analysis.playlists.push(lines[i + 1]);
                    }
                }
            }
        }
        // Check if it's a media playlist
        else if (content.includes('#EXTINF:')) {
            analysis.type = 'media';
            
            for (const line of lines) {
                if (line.startsWith('#EXTINF:')) {
                    const durationMatch = line.match(/#EXTINF:([0-9.]+)/);
                    if (durationMatch) {
                        analysis.duration += parseFloat(durationMatch[1]);
                        analysis.segments++;
                    }
                }
            }
            
            analysis.duration = Math.round(analysis.duration / 60); // Convert to minutes
        }
        
        return analysis;
    }

    async researchPlayerURLs() {
        console.log(`🚀 Starting 24-HD Player URL Research`);
        console.log(`=`.repeat(80));
        
        // URLs from the log that returned HTTP 200 with 32118 bytes
        const testUrls = [
            'https://main.24playerhd.com/index_th.php?id=05903f9cbec01c2f&b=26133',
            'https://main.24playerhd.com/index_th.php?id=05903f9cbec01c2f&b=26133&backup=1',
            'https://main.24playerhd.com/index_th.php?id=a24904e5d3ed28ea&b=26133',
            'https://main.24playerhd.com/index_th.php?id=a24904e5d3ed28ea&b=26133&backup=1',
            'https://main.24playerhd.com/index_th.php?id=26133&server=1&backup=1'
        ];
        
        for (const url of testUrls) {
            console.log(`\n${'='.repeat(80)}`);
            console.log(`🎬 Testing: ${url}`);
            console.log(`${'='.repeat(80)}`);
            
            const result = await this.analyzePlayerURL(url);
            
            if (result && (result.m3u8Urls.length > 0 || result.videoSources.length > 0)) {
                console.log(`✅ Found working player URL!`);
                break;
            }
            
            console.log(`⏳ Waiting 2 seconds before next test...`);
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        console.log(`\n✅ Player URL research completed!`);
    }
}

// Run research
const researcher = new HD24PlayerResearcher();
researcher.researchPlayerURLs().catch(console.error);
