<?php

echo "🚀 Testing 24-HD.com patterns with saved HTML...\n";

$html_file = '24hd_analysis/main_page.html';
if (!file_exists($html_file)) {
    echo "❌ HTML file not found: $html_file\n";
    exit(1);
}

echo "📄 Testing with: $html_file\n";

$html = file_get_contents($html_file);
echo "✅ HTML loaded: " . strlen($html) . " characters\n";

echo "\n📝 Testing title extraction...\n";
$title_patterns = [
    '/<div[^>]*class=["\'][^"\']*movietext[^"\']*["\'][^>]*>[\s\S]*?<h1[^>]*>([^<]+)<\/h1>/i',
    '/<h1[^>]*>([^<]+)<\/h1>/i',
    '/<meta[^>]*property=["\']og:title["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i',
    '/<title[^>]*>ดูหนัง\s*([^<]+?)\s*เต็มเรื่อง[^<]*<\/title>/i'
];

foreach ($title_patterns as $i => $pattern) {
    if (preg_match($pattern, $html, $matches)) {
        $title = trim(strip_tags($matches[1]));
        $title = preg_replace('/^ดูหนัง\s*/i', '', $title);
        $title = preg_replace('/\s*เต็มเรื่อง.*$/i', '', $title);

        if (!empty($title) && strlen($title) > 3 &&
            strpos($title, 'เว็บดูหนัง') === false &&
            strpos($title, 'Netflix ฟรี') === false &&
            strpos($title, 'ดูหนังใหม่') === false) {
            echo "✅ Pattern " . ($i + 1) . " found title: $title\n";
            echo "   Pattern used: $pattern\n";
            break;
        } else {
            echo "⚠️ Pattern " . ($i + 1) . " found but filtered out: $title\n";
        }
    }
}

echo "\n⭐ Testing IMDb rating extraction...\n";
$imdb_patterns = [
    '/<span[^>]*class=["\']*score["\']*[^>]*>[\s\S]*?<\/noscript>([0-9]+\.?[0-9]*)/i',
    '/<span[^>]*class=["\']*score["\']*[^>]*>[\s\S]*?([0-9]+\.?[0-9]*)/i'
];

foreach ($imdb_patterns as $i => $pattern) {
    if (preg_match($pattern, $html, $matches)) {
        $rating = floatval($matches[1]);
        if ($rating >= 0 && $rating <= 10) {
            echo "✅ Pattern " . ($i + 1) . " found IMDb rating: $rating\n";
            break;
        }
    }
}

echo "\n🎬 Testing YouTube ID extraction...\n";
$youtube_patterns = [
    '/videoId:\s*["\']([a-zA-Z0-9_-]+)["\']/',
    '/youtube\.com\/vi\/([a-zA-Z0-9_-]+)/'
];

foreach ($youtube_patterns as $i => $pattern) {
    if (preg_match($pattern, $html, $matches)) {
        $youtube_id = $matches[1];
        if (preg_match('/^[a-zA-Z0-9_-]{10,12}$/', $youtube_id)) {
            echo "✅ Pattern " . ($i + 1) . " found YouTube ID: $youtube_id\n";
            break;
        }
    }
}

echo "\n🖼️ Testing poster image extraction...\n";
$poster_patterns = [
    '/<meta[^>]*property=["\']og:image["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i'
];

foreach ($poster_patterns as $i => $pattern) {
    if (preg_match($pattern, $html, $matches)) {
        $poster_url = trim($matches[1]);
        echo "✅ Pattern " . ($i + 1) . " found poster: $poster_url\n";
        break;
    }
}

echo "\n🎮 Testing player button extraction...\n";
$button_pattern = '/<span[^>]+class=["\']*halim-btn[^"\']*["\']*[^>]+data-post-id=["\']*([^"\']+)["\']*[^>]+data-server=["\']*([^"\']+)["\']*[^>]*>/i';

if (preg_match_all($button_pattern, $html, $matches, PREG_SET_ORDER)) {
    echo "✅ Found " . count($matches) . " player buttons:\n";
    foreach ($matches as $i => $match) {
        $post_id = $match[1];
        $server = $match[2];
        echo "   " . ($i + 1) . ". Post ID: $post_id, Server: $server\n";
    }
} else {
    echo "❌ No player buttons found\n";
}

echo "\n✅ Pattern testing completed!\n";
?>
