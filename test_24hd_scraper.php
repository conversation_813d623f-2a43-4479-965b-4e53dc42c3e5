<?php

echo "🚀 Testing 24-HD.com patterns with saved HTML...\n";

$html_file = '24hd_analysis/main_page.html';
if (!file_exists($html_file)) {
    echo "❌ HTML file not found: $html_file\n";
    exit(1);
}

echo "📄 Testing with: $html_file\n";

$html = file_get_contents($html_file);
echo "✅ HTML loaded: " . strlen($html) . " characters\n";

echo "\n📝 Testing title extraction...\n";
$title_patterns = [
    '/<div[^>]*class=["\'][^"\']*movietext[^"\']*["\'][^>]*>[\s\S]*?<h1[^>]*>([^<]+)<\/h1>/i',
    '/<h1[^>]*>([^<]+)<\/h1>/i',
    '/<meta[^>]*property=["\']og:title["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i',
    '/<title[^>]*>ดูหนัง\s*([^<]+?)\s*เต็มเรื่อง[^<]*<\/title>/i'
];

foreach ($title_patterns as $i => $pattern) {
    if (preg_match($pattern, $html, $matches)) {
        $title = trim(strip_tags($matches[1]));
        $title = preg_replace('/^ดูหนัง\s*/i', '', $title);
        $title = preg_replace('/\s*เต็มเรื่อง.*$/i', '', $title);

        if (!empty($title) && strlen($title) > 3 &&
            strpos($title, 'เว็บดูหนัง') === false &&
            strpos($title, 'Netflix ฟรี') === false &&
            strpos($title, 'ดูหนังใหม่') === false) {
            echo "✅ Pattern " . ($i + 1) . " found title: $title\n";
            echo "   Pattern used: $pattern\n";
            break;
        } else {
            echo "⚠️ Pattern " . ($i + 1) . " found but filtered out: $title\n";
        }
    }
}

echo "\n⭐ Testing IMDb rating extraction...\n";
$imdb_patterns = [
    '/<span[^>]*class=["\']*score["\']*[^>]*>[\s\S]*?<\/noscript>([0-9]+\.?[0-9]*)/i',
    '/<span[^>]*class=["\']*score["\']*[^>]*>[\s\S]*?([0-9]+\.?[0-9]*)/i'
];

foreach ($imdb_patterns as $i => $pattern) {
    if (preg_match($pattern, $html, $matches)) {
        $rating = floatval($matches[1]);
        if ($rating >= 0 && $rating <= 10) {
            echo "✅ Pattern " . ($i + 1) . " found IMDb rating: $rating\n";
            break;
        }
    }
}

echo "\n🎬 Testing YouTube ID extraction...\n";
$youtube_patterns = [
    '/videoId:\s*["\']([a-zA-Z0-9_-]+)["\']/',
    '/youtube\.com\/vi\/([a-zA-Z0-9_-]+)/'
];

foreach ($youtube_patterns as $i => $pattern) {
    if (preg_match($pattern, $html, $matches)) {
        $youtube_id = $matches[1];
        if (preg_match('/^[a-zA-Z0-9_-]{10,12}$/', $youtube_id)) {
            echo "✅ Pattern " . ($i + 1) . " found YouTube ID: $youtube_id\n";
            break;
        }
    }
}

echo "\n🖼️ Testing poster image extraction...\n";
$poster_patterns = [
    '/<meta[^>]*property=["\']og:image["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i'
];

foreach ($poster_patterns as $i => $pattern) {
    if (preg_match($pattern, $html, $matches)) {
        $poster_url = trim($matches[1]);
        echo "✅ Pattern " . ($i + 1) . " found poster: $poster_url\n";
        break;
    }
}

echo "\n🎮 Testing player button extraction...\n";
$button_pattern = '/<span[^>]+class=["\']*halim-btn[^"\']*["\']*[^>]+data-post-id=["\']*([^"\']+)["\']*[^>]+data-server=["\']*([^"\']+)["\']*[^>]*>/i';

if (preg_match_all($button_pattern, $html, $matches, PREG_SET_ORDER)) {
    echo "✅ Found " . count($matches) . " player buttons:\n";
    foreach ($matches as $i => $match) {
        $post_id = $match[1];
        $server = $match[2];
        echo "   " . ($i + 1) . ". Post ID: $post_id, Server: $server\n";
    }
} else {
    echo "❌ No player buttons found\n";
}

echo "\n🔑 Testing nonce extraction...\n";
$nonce_patterns = [
    '/ajax_player\s*=\s*\{[^}]*"nonce"\s*:\s*"([^"]*)"/i',
    '/"nonce"\s*:\s*"([^"]*)"/i'
];

foreach ($nonce_patterns as $i => $pattern) {
    if (preg_match($pattern, $html, $matches)) {
        $nonce = $matches[1];
        echo "✅ Pattern " . ($i + 1) . " found nonce: '$nonce' (length: " . strlen($nonce) . ")\n";
        break;
    }
}

echo "\n📝 Testing API title extraction...\n";
$title_patterns = [
    '/halim_cfg\s*=\s*\{[^}]*"post_title"\s*:\s*"([^"]*)"/i',
    '/"post_title"\s*:\s*"([^"]*)"/i'
];

foreach ($title_patterns as $i => $pattern) {
    if (preg_match($pattern, $html, $matches)) {
        $title = $matches[1];
        echo "✅ Pattern " . ($i + 1) . " found API title: '$title' (length: " . strlen($title) . ")\n";
        break;
    }
}

echo "\n🔢 Testing post_id from halim_cfg...\n";
if (preg_match('/"post_id"\s*:\s*(\d+)/i', $html, $matches)) {
    $cfg_post_id = $matches[1];
    echo "✅ Found halim_cfg post_id: $cfg_post_id\n";
}

echo "\n🧪 Testing API call with Final Destination 4 data...\n";

$test_cases = [
    ['post_id' => 29814, 'server' => 1, 'name' => 'Final Destination 4 - Server 1'],
    ['post_id' => 35854, 'server' => 1, 'name' => '11 Rebels - Server 1'],
    ['post_id' => 35854, 'server' => 2, 'name' => '11 Rebels - Server 2']
];

$ajax_url = 'https://api.24-hd.com/get.php';
$nonce = '';
$title = '';

foreach ($test_cases as $test) {
    echo "\n🔍 Testing {$test['name']} (Post ID: {$test['post_id']}, Server: {$test['server']})...\n";
    $form_data = "action=halim_ajax_player&nonce=$nonce&episode=1&server={$test['server']}&postid={$test['post_id']}&lang=&title=" . urlencode($title);

    echo "Form data: $form_data\n";

    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $ajax_url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $form_data,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/x-www-form-urlencoded',
                'Accept: */*',
                'Referer: https://www.24-hd.com/',
                'Origin: https://www.24-hd.com'
            ],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        echo "   HTTP Code: $http_code\n";
        echo "   Response length: " . strlen($response) . " chars\n";

        if ($response && strpos($response, 'ไม่พบ รายการ') === false) {
            file_put_contents("api_response_{$test['post_id']}_{$test['server']}.html", $response);
            echo "   ✅ Success! Response saved to: api_response_{$test['post_id']}_{$test['server']}.html\n";
            echo "   Response preview: " . substr($response, 0, 200) . "...\n";

            // Check for iframe
            if (preg_match('/<iframe[^>]+src=["\']*([^"\']+)["\']*[^>]*>/i', $response, $matches)) {
                echo "   🎬 Found iframe: {$matches[1]}\n";
            }
            break;
        } else {
            echo "   ❌ Failed or 'not found' response\n";
        }
    } else {
        echo "   cURL not available\n";
    }
}

echo "\n✅ Pattern testing completed!\n";
?>
