<?php

if (!defined('ABSPATH')) {
    exit;
}

require_once __DIR__ . '/BaseScraper.php';

class MovieScraper extends BaseScraper {

    public function __construct() {
        parent::__construct();
        $this->upload_dir .= 'movies/';
        $this->base_url .= 'movies/';

        if (!file_exists($this->upload_dir)) {
            wp_mkdir_p($this->upload_dir);
        }
    }

    public function scrape($post_id, $movie_url, $options = []) {
        try {
            $progress = ScrapingProgressManager::getInstance()->initializeForPost($post_id, 'movie');

            $this->log("Starting movie scraping for post ID: $post_id, URL: $movie_url");
            $progress->updateProgress('init', 0);

            $change_type = $progress->checkDomainChange($movie_url);
            $progress->updateProgress('domain_check', 50);

            $should_clear = $this->handle_source_change($post_id, $change_type, $movie_url);
            update_post_meta($post_id, 'scraper_last_change_type', $change_type);

            if ($should_clear) {
                $this->log("Cleared old data due to source change");
            }
            $progress->completeStep('domain_check');

            $website = $this->detect_website($movie_url);
            $this->log("Detected website: $website");

            switch ($website) {
                case '22-hdd':
                    $progress->updateProgress('fetch_html', 0, 'เชื่อมต่อ 22HDD');
                    $scraper = new HD22MovieScraper();
                    return $scraper->scrape($post_id, $movie_url, $options);

                case '24-hd':
                    $progress->updateProgress('fetch_html', 0, 'เชื่อมต่อ 24HD');
                    $scraper = new HD24MovieScraper();
                    return $scraper->scrape($post_id, $movie_url, $options);

                default:
                    $error_msg = "Unsupported website: $website. Please add scraper support for this website.";
                    $this->log("ERROR: $error_msg");
                    $progress->setError($error_msg, 'detect_website');
                    return false;
            }

        } catch (Exception $e) {
            $error_msg = 'Exception occurred: ' . $e->getMessage();
            $this->log("ERROR: $error_msg");
            $progress->setError($error_msg);
            return false;
        }
    }

    protected function process_content($post_id, $config, $options = []) {
        return false;
    }

    public function get_scraping_status($post_id) {
        $base_status = [
            'status' => get_post_meta($post_id, 'scraping_status', true),
            'message' => get_post_meta($post_id, 'scraping_message', true),
            'updated' => get_post_meta($post_id, 'scraping_updated', true)
        ];

        foreach (['dubbed', 'subbed'] as $language) {
            $base_status[$language] = [
                'status' => get_post_meta($post_id, "scraping_{$language}_status", true),
                'message' => get_post_meta($post_id, "scraping_{$language}_message", true),
                'files' => get_post_meta($post_id, "scraping_{$language}_files", true),
                'url' => get_post_meta($post_id, "scraping_{$language}_url", true),
                'updated' => get_post_meta($post_id, "scraping_{$language}_updated", true)
            ];
        }

        return $base_status;
    }

    public function clear_scraping_data($post_id) {
        parent::clear_scraping_data($post_id);

        $additional_keys = [
            'dubbed_master_original_url', 'subbed_master_original_url',
            'movie_url_dubbed', 'movie_url_subbed', 'scraping_video_unavailable'
        ];

        foreach (['dubbed', 'subbed'] as $language) {
            $additional_keys[] = "scraping_{$language}_status";
            $additional_keys[] = "scraping_{$language}_message";
            $additional_keys[] = "scraping_{$language}_files";
            $additional_keys[] = "scraping_{$language}_url";
            $additional_keys[] = "scraping_{$language}_updated";
        }

        foreach ($additional_keys as $key) {
            delete_post_meta($post_id, $key);
        }

        $this->log("Movie scraping data cleared for post ID: $post_id");
    }
}

class HD22MovieScraper extends BaseScraper {

    public function __construct() {
        parent::__construct();
        $this->upload_dir .= 'movies/';
        $this->base_url .= 'movies/';

        if (!file_exists($this->upload_dir)) {
            wp_mkdir_p($this->upload_dir);
        }
    }

    public function scrape($post_id, $movie_url, $options = []) {
        try {
            $progress = ScrapingProgressManager::getInstance();

            $progress->updateProgress('fetch_html', 0);
            $html = $this->fetch_html($movie_url);
            if (!$html) {
                $error_msg = 'Could not fetch HTML from URL: ' . $movie_url;
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'fetch_html');
                return false;
            }
            $progress->completeStep('fetch_html');

            $this->log("Successfully fetched HTML, extracting movie information...");
            $progress->updateProgress('extract_metadata', 0);
            $this->extract_metadata($post_id, $html);
            $progress->completeStep('extract_metadata');

            $progress->updateProgress('find_player', 0);
            $main_player_url = $this->extract_main_player($html);
            if (!$main_player_url) {
                $error_msg = 'No main player iframe found in HTML';
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'find_player');
                return false;
            }
            $progress->completeStep('find_player');

            $this->log("Found main player URL: $main_player_url");

            $progress->updateProgress('fetch_player', 0);
            $player_html = $this->fetch_html($main_player_url);
            if (!$player_html) {
                $error_msg = 'Could not fetch player HTML from URL: ' . $main_player_url;
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'fetch_player');
                return false;
            }
            $progress->completeStep('fetch_player');

            $this->log("Successfully fetched player HTML, extracting movie config...");
            $progress->updateProgress('extract_config', 0);

            $movie_config = $this->extract_config($player_html);
            if (!$movie_config) {
                $error_msg = 'No movieList configuration found in player HTML';
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'extract_config');
                return false;
            }
            $progress->completeStep('extract_config');

            return $this->process_content($post_id, $movie_config, $options);

        } catch (Exception $e) {
            $error_msg = 'Exception occurred: ' . $e->getMessage();
            $this->log("ERROR: $error_msg");
            $progress->setError($error_msg);
            return false;
        }
    }

    protected function process_content($post_id, $config, $options = []) {
        $progress = ScrapingProgressManager::getInstance();

        $this->log("Processing movie content for post ID: $post_id");
        $progress->updateProgress('process_dubbed', 0);

        $language_players = $this->detect_language_players($config);

        $results = [];
        $video_unavailable = false;

        $progress->updateProgress('process_dubbed', 10);
        $lang_display = 'พากย์ไทย';
        if (empty($language_players['dubbed'])) {
            $this->log("No dubbed players found");
            $progress->updateProgress('process_dubbed', 50, "ไม่พบไฟล์ $lang_display");
            $results['dubbed'] = false;
        } else {
            $progress->updateProgress('process_dubbed', 20, "กำลังดึงไฟล์ $lang_display");
            $result = $this->process_language_video($post_id, 'dubbed', $language_players['dubbed']);

            if ($result === 'HTTP_500_ERROR' || $result === 'HTTP_404_ERROR') {
                $video_unavailable = true;
                $results['dubbed'] = 'unavailable';
                $progress->updateProgress('process_dubbed', 90, "ไฟล์ $lang_display ไม่พร้อมใช้งาน");
            } else {
                $results['dubbed'] = $result;
                $progress->updateProgress('process_dubbed', 95, "ดึงไฟล์ $lang_display สำเร็จ");
            }
        }
        $progress->completeStep('process_dubbed');

        $progress->updateProgress('process_subbed', 10);
        $lang_display = 'ซับไทย';
        if (empty($language_players['subbed'])) {
            $this->log("No subbed players found");
            $progress->updateProgress('process_subbed', 50, "ไม่พบไฟล์ $lang_display");
            $results['subbed'] = false;
        } else {
            $progress->updateProgress('process_subbed', 20, "กำลังดึงไฟล์ $lang_display");
            $result = $this->process_language_video($post_id, 'subbed', $language_players['subbed']);

            if ($result === 'HTTP_500_ERROR' || $result === 'HTTP_404_ERROR') {
                $video_unavailable = true;
                $results['subbed'] = 'unavailable';
                $progress->updateProgress('process_subbed', 90, "ไฟล์ $lang_display ไม่พร้อมใช้งาน");
            } else {
                $results['subbed'] = $result;
                $progress->updateProgress('process_subbed', 95, "ดึงไฟล์ $lang_display สำเร็จ");
            }
        }
        $progress->completeStep('process_subbed');

        $progress->updateProgress('finalize', 0);

        if ($video_unavailable && !array_filter($results, function($r) { return $r !== false && $r !== 'unavailable'; })) {
            $this->log("All video sources are unavailable");
            $progress->setVideoUnavailable('ไฟล์วิดีโอไม่พร้อมใช้งาน');
            update_post_meta($post_id, 'scraping_video_unavailable', true);
            return 'video_unavailable';
        }

        $success_count = count(array_filter($results, function($r) { return $r !== false && $r !== 'unavailable'; }));

        if ($success_count > 0) {
            $this->auto_fill_m3u8_urls($post_id, $results);
            $progress->updateProgress('finalize', 80);
            $progress->setCompleted("ดึงข้อมูลสำเร็จ $success_count ภาษา");
            $this->log("Successfully processed $success_count language(s)");
            return true;
        }

        $progress->setError('ไม่พบไฟล์วิดีโอที่ใช้งานได้', 'finalize');
        return false;
    }

    protected function process_language_video($post_id, $language, $players) {
        $this->log("Processing $language video for post ID: $post_id with " . count($players) . " players");

        $total_players = count($players);
        $failed_players = 0;
        $http_errors = [];

        foreach ($players as $index => $player_data) {
            $player_url = $player_data['url'];
            $player_group = $player_data['group'];

            $this->log("Trying $language player " . ($index + 1) . "/$total_players: $player_url (Group: $player_group)");

            $m3u8_files = $this->extract_m3u8_files($player_url);

            if ($m3u8_files === 'HTTP_500_ERROR') {
                $this->log("HTTP 500 Error from $language player " . ($index + 1));
                $http_errors[] = "Player " . ($index + 1) . ": HTTP 500 Error";
                $failed_players++;
                continue;
            }

            if ($m3u8_files === 'HTTP_404_ERROR') {
                $this->log("HTTP 404 Error from $language player " . ($index + 1));
                $http_errors[] = "Player " . ($index + 1) . ": HTTP 404 Error";
                $failed_players++;
                continue;
            }

            if (empty($m3u8_files)) {
                $this->log("No M3U8 files found for $language player " . ($index + 1));
                $failed_players++;
                continue;
            }

            $this->log("Found " . count($m3u8_files) . " M3U8 files for $language player " . ($index + 1));

            $saved_files = $this->save_m3u8_files($post_id, 'movie', $language, $m3u8_files);

            if ($saved_files) {
                $this->log("Successfully saved $language M3U8 files from player " . ($index + 1));

                update_post_meta($post_id, "scraping_{$language}_status", 'completed');
                update_post_meta($post_id, "scraping_{$language}_message", 'Successfully processed');
                update_post_meta($post_id, "scraping_{$language}_files", $saved_files);
                update_post_meta($post_id, "scraping_{$language}_url", $player_url);
                update_post_meta($post_id, "scraping_{$language}_updated", current_time('mysql'));

                if (isset($m3u8_files['master_original']['url'])) {
                    update_post_meta($post_id, "{$language}_master_original_url", $m3u8_files['master_original']['url']);
                    $this->log("$language original master URL saved: " . $m3u8_files['master_original']['url']);
                }

                return $saved_files;
            } else {
                $this->log("Failed to save $language M3U8 files from player " . ($index + 1));
                $failed_players++;
            }
        }

        $this->log("All $language players failed. Total: $total_players, Failed: $failed_players");

        if (!empty($http_errors)) {
            $error_message = "Video sources unavailable: " . implode(", ", $http_errors);
            $this->log("HTTP errors detected for $language: $error_message");
            update_post_meta($post_id, "scraping_{$language}_status", 'http_error');
            update_post_meta($post_id, "scraping_{$language}_message", $error_message);
            return 'HTTP_500_ERROR';
        } else {
            update_post_meta($post_id, "scraping_{$language}_status", 'failed');
            update_post_meta($post_id, "scraping_{$language}_message", 'No valid M3U8 files found from any player');
        }

        return false;
    }

    protected function auto_fill_m3u8_urls($post_id, $results) {
        $this->log("Auto-filling M3U8 URLs for post ID: $post_id");
        
        $scraper_settings = get_option('movie_scraper_settings', []);
        $auto_fill_m3u8 = $scraper_settings['auto_fill_m3u8'] ?? true;
        
        if (!$auto_fill_m3u8) {
            $this->log("Auto-fill M3U8 is disabled");
            return;
        }
        
        $use_downloaded_files = $scraper_settings['use_downloaded_files'] ?? true;
        
        if ($use_downloaded_files) {
            if (isset($results['dubbed']) && $results['dubbed'] && isset($results['dubbed']['master'])) {
                if (!get_post_meta($post_id, 'm3u8_dubbed', true)) {
                    update_post_meta($post_id, 'm3u8_dubbed', $results['dubbed']['master']);
                    $this->log("Auto-filled M3U8 dubbed URL (downloaded): " . $results['dubbed']['master']);
                }
            }
            
            if (isset($results['subbed']) && $results['subbed'] && isset($results['subbed']['master'])) {
                if (!get_post_meta($post_id, 'm3u8_subbed', true)) {
                    update_post_meta($post_id, 'm3u8_subbed', $results['subbed']['master']);
                    $this->log("Auto-filled M3U8 subbed URL (downloaded): " . $results['subbed']['master']);
                }
            }
        } else {
            $dubbed_original_url = get_post_meta($post_id, 'dubbed_master_original_url', true);
            if ($dubbed_original_url) {
                update_post_meta($post_id, 'movie_url_dubbed', $dubbed_original_url);
                $this->log("Auto-filled dubbed URL (original): " . $dubbed_original_url);
            }
            
            $subbed_original_url = get_post_meta($post_id, 'subbed_master_original_url', true);
            if ($subbed_original_url) {
                update_post_meta($post_id, 'movie_url_subbed', $subbed_original_url);
                $this->log("Auto-filled subbed URL (original): " . $subbed_original_url);
            }
        }
    }
}

class HD24MovieScraper extends BaseScraper {

    public function __construct() {
        parent::__construct();
        $this->upload_dir .= 'movies/';
        $this->base_url .= 'movies/';

        if (!file_exists($this->upload_dir)) {
            wp_mkdir_p($this->upload_dir);
        }
    }

    public function scrape($post_id, $movie_url, $options = []) {
        try {
            $progress = ScrapingProgressManager::getInstance();

            $progress->updateProgress('fetch_html', 0);
            $html = $this->fetch_html($movie_url);
            if (!$html) {
                $error_msg = 'Could not fetch HTML from URL: ' . $movie_url;
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'fetch_html');
                return false;
            }
            $progress->completeStep('fetch_html');

            $this->log("Successfully fetched HTML, extracting movie information...");
            $progress->updateProgress('extract_metadata', 0);
            $this->extract_metadata($post_id, $html);
            $progress->completeStep('extract_metadata');

            $progress->updateProgress('find_player', 0);
            $main_player_url = $this->extract_24hd_main_player($html);
            if (!$main_player_url) {
                $error_msg = 'No main player iframe found in HTML';
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'find_player');
                return false;
            }
            $progress->completeStep('find_player');

            $this->log("Found main player URL: $main_player_url");

            $progress->updateProgress('fetch_player', 0);
            $player_html = $this->fetch_html($main_player_url);
            if (!$player_html) {
                $error_msg = 'Could not fetch player HTML from URL: ' . $main_player_url;
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'fetch_player');
                return false;
            }
            $progress->completeStep('fetch_player');

            $this->log("Successfully fetched player HTML, extracting movie config...");
            $progress->updateProgress('extract_config', 0);

            $movie_config = $this->extract_24hd_config($player_html);
            if (!$movie_config) {
                $error_msg = 'No movieList configuration found in player HTML';
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'extract_config');
                return false;
            }
            $progress->completeStep('extract_config');

            return $this->process_content($post_id, $movie_config, $options);

        } catch (Exception $e) {
            $error_msg = 'Exception occurred: ' . $e->getMessage();
            $this->log("ERROR: $error_msg");
            $progress->setError($error_msg);
            return false;
        }
    }

    protected function extract_24hd_main_player($html) {
        $this->log("Extracting 24-HD main player from HTML");
        
        $patterns = [
            '/<iframe[^>]+src=["\']([^"\']*24hd\.com[^"\']*)["\'][^>]*>/i',
            '/<iframe[^>]+src=["\']([^"\']*player\.24hd\.com[^"\']*)["\'][^>]*>/i',
            '/<iframe[^>]+src=["\']([^"\']*embed\.24hd\.com[^"\']*)["\'][^>]*>/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $iframe_src = $matches[1];
                if (strpos($iframe_src, 'youtube.com') === false && 
                    strpos($iframe_src, 'youtu.be') === false) {
                    $this->log("Found 24-HD main player: $iframe_src");
                    return $iframe_src;
                }
            }
        }
        
        $this->log("No suitable 24-HD main player found");
        return false;
    }

    protected function extract_24hd_config($html) {
        $this->log("Extracting 24-HD configuration from HTML");
        
        $config_patterns = [
            'movieList' => [
                '/movieList\s*=\s*(\{[^;]*\})/s',
                '/var\s+movieList\s*=\s*(\{[^;]*\})/s',
                '/movieList\s*=\s*(\{(?:[^{}]*|{[^{}]*})*\})/s'
            ],
            'playerConfig' => [
                '/playerConfig\s*=\s*(\{[^;]*\})/s',
                '/var\s+playerConfig\s*=\s*(\{[^;]*\})/s',
                '/playerConfig\s*=\s*(\{(?:[^{}]*|{[^{}]*})*\})/s'
            ],
            '24hd_config' => [
                '/24hd_config\s*=\s*(\{[^;]*\})/s',
                '/var\s+24hd_config\s*=\s*(\{[^;]*\})/s'
            ]
        ];
        
        foreach ($config_patterns as $type => $patterns) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $html, $matches)) {
                    $this->log("Found $type configuration with pattern");
                    
                    $json_str = $matches[1];
                    $config = $this->parse_config_json($json_str);
                    
                    if ($config !== false) {
                        $this->log("Successfully parsed $type configuration");
                        $this->log("Config structure: " . json_encode(array_keys($config), JSON_UNESCAPED_UNICODE));
                        return $config;
                    } else {
                        $this->log("Failed to parse $type JSON, trying next pattern");
                    }
                }
            }
        }
        
        $this->log("No valid configuration found, trying 24-HD specific extraction");
        return $this->extract_24hd_config_alternative($html);
    }

    protected function extract_24hd_config_alternative($html) {
        $this->log("Trying 24-HD specific config extraction methods");
        
        $alternative_patterns = [
            '/window\.movieList\s*=\s*(\{[^;]*\})/s',
            '/window\.playerConfig\s*=\s*(\{[^;]*\})/s',
            '/var\s+config\s*=\s*(\{[^;]*\})/s',
            '/data\s*:\s*(\{[^}]*"link"[^}]*\})/s',
            '/(\{[^{}]*"link"\s*:\s*\{[^{}]*"thai"[^{}]*\}[^{}]*\})/s',
            '/24hd\.com[^}]*"link"[^}]*\}/s'
        ];
        
        foreach ($alternative_patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $this->log("Found 24-HD alternative config pattern");
                $json_str = $matches[1];
                $config = $this->parse_config_json($json_str);
                
                if ($config !== false && $this->validate_24hd_config_structure($config)) {
                    $this->log("Successfully parsed 24-HD alternative configuration");
                    return $config;
                }
            }
        }
        
        $this->log("Searching for 24-HD inline data structures");
        if (preg_match_all('/(\{[^{}]*"MU_url"[^{}]*\})/s', $html, $matches)) {
            $this->log("Found " . count($matches[0]) . " potential 24-HD player objects");
            
            $synthetic_config = [
                'link' => [
                    'thai' => [],
                    'subthai' => [],
                    'sub' => []
                ]
            ];
            
            foreach ($matches[0] as $match) {
                $player_data = $this->parse_config_json($match);
                if ($player_data && isset($player_data['MU_url'])) {
                    $sound = $player_data['MU_sound'] ?? 'thai';
                    if (in_array($sound, ['thai', 'subthai', 'sub'])) {
                        $synthetic_config['link'][$sound][] = $player_data;
                    }
                }
            }
            
            if (!empty($synthetic_config['link']['thai']) || !empty($synthetic_config['link']['subthai']) || !empty($synthetic_config['link']['sub'])) {
                $this->log("Created synthetic 24-HD config from inline data");
                return $synthetic_config;
            }
        }
        
        $this->log("All 24-HD config extraction methods failed");
        return false;
    }

    protected function validate_24hd_config_structure($config) {
        if (!is_array($config)) {
            return false;
        }
        
        if (isset($config['link']) && is_array($config['link'])) {
            foreach (['thai', 'subthai', 'sub'] as $lang) {
                if (isset($config['link'][$lang]) && is_array($config['link'][$lang])) {
                    return true;
                }
            }
        }
        
        return $this->has_nested_language_structure($config);
    }

    protected function process_content($post_id, $config, $options = []) {
        $progress = ScrapingProgressManager::getInstance();

        $this->log("Processing 24-HD movie content for post ID: $post_id");
        $progress->updateProgress('process_dubbed', 0);

        $language_players = $this->detect_24hd_language_players($config);

        $results = [];
        $video_unavailable = false;

        $progress->updateProgress('process_dubbed', 10);
        $lang_display = 'พากย์ไทย';
        if (empty($language_players['dubbed'])) {
            $this->log("No dubbed players found");
            $progress->updateProgress('process_dubbed', 50, "ไม่พบไฟล์ $lang_display");
            $results['dubbed'] = false;
        } else {
            $progress->updateProgress('process_dubbed', 20, "กำลังดึงไฟล์ $lang_display");
            $result = $this->process_24hd_language_video($post_id, 'dubbed', $language_players['dubbed']);

            if ($result === 'HTTP_500_ERROR' || $result === 'HTTP_404_ERROR') {
                $video_unavailable = true;
                $results['dubbed'] = 'unavailable';
                $progress->updateProgress('process_dubbed', 90, "ไฟล์ $lang_display ไม่พร้อมใช้งาน");
            } else {
                $results['dubbed'] = $result;
                $progress->updateProgress('process_dubbed', 95, "ดึงไฟล์ $lang_display สำเร็จ");
            }
        }
        $progress->completeStep('process_dubbed');

        $progress->updateProgress('process_subbed', 10);
        $lang_display = 'ซับไทย';
        if (empty($language_players['subbed'])) {
            $this->log("No subbed players found");
            $progress->updateProgress('process_subbed', 50, "ไม่พบไฟล์ $lang_display");
            $results['subbed'] = false;
        } else {
            $progress->updateProgress('process_subbed', 20, "กำลังดึงไฟล์ $lang_display");
            $result = $this->process_24hd_language_video($post_id, 'subbed', $language_players['subbed']);

            if ($result === 'HTTP_500_ERROR' || $result === 'HTTP_404_ERROR') {
                $video_unavailable = true;
                $results['subbed'] = 'unavailable';
                $progress->updateProgress('process_subbed', 90, "ไฟล์ $lang_display ไม่พร้อมใช้งาน");
            } else {
                $results['subbed'] = $result;
                $progress->updateProgress('process_subbed', 95, "ดึงไฟล์ $lang_display สำเร็จ");
            }
        }
        $progress->completeStep('process_subbed');

        $progress->updateProgress('finalize', 0);

        if ($video_unavailable && !array_filter($results, function($r) { return $r !== false && $r !== 'unavailable'; })) {
            $this->log("All video sources are unavailable");
            $progress->setVideoUnavailable('ไฟล์วิดีโอไม่พร้อมใช้งาน');
            update_post_meta($post_id, 'scraping_video_unavailable', true);
            return 'video_unavailable';
        }

        $success_count = count(array_filter($results, function($r) { return $r !== false && $r !== 'unavailable'; }));

        if ($success_count > 0) {
            $this->auto_fill_m3u8_urls($post_id, $results);
            $progress->updateProgress('finalize', 80);
            $progress->setCompleted("ดึงข้อมูลสำเร็จ $success_count ภาษา");
            $this->log("Successfully processed $success_count language(s)");
            return true;
        }

        $progress->setError('ไม่พบไฟล์วิดีโอที่ใช้งานได้', 'finalize');
        return false;
    }

    protected function detect_24hd_language_players($config) {
        $this->log("Detecting 24-HD language players from config");
        
        $language_players = [
            'dubbed' => [],
            'subbed' => []
        ];
        
        $this->extract_24hd_language_players_recursive($config, $language_players);
        
        foreach ($language_players as $lang => $players) {
            $sorted_players = $this->prioritize_players_by_group($players);
            $language_players[$lang] = $sorted_players;
            $this->log("Found " . count($players) . " $lang players");
        }
        
        return $language_players;
    }

    protected function extract_24hd_language_players_recursive($data, &$language_players) {
        if (!is_array($data)) {
            return;
        }
        
        foreach ($data as $key => $value) {
            if ($key === 'link' && is_array($value)) {
                foreach (['thai', 'subthai', 'sub'] as $original_lang) {
                    if (isset($value[$original_lang]) && is_array($value[$original_lang])) {
                        $target_lang = ($original_lang === 'thai') ? 'dubbed' : 'subbed';
                        foreach ($value[$original_lang] as $player) {
                            if (isset($player['MU_url']) && !empty($player['MU_url'])) {
                                $language_players[$target_lang][] = [
                                    'url' => $player['MU_url'],
                                    'group' => $player['MU_group'] ?? 0,
                                    'type' => $player['MU_type'] ?? 'embed',
                                    'sound' => $player['MU_sound'] ?? $original_lang,
                                    'id' => $player['MU_id'] ?? null,
                                    'original_lang' => $original_lang
                                ];
                            }
                        }
                    }
                }
            } elseif (in_array($key, ['thai', 'subthai', 'sub']) && is_array($value)) {
                $target_lang = ($key === 'thai') ? 'dubbed' : 'subbed';
                foreach ($value as $player) {
                    if (isset($player['MU_url']) && !empty($player['MU_url'])) {
                        $language_players[$target_lang][] = [
                            'url' => $player['MU_url'],
                            'group' => $player['MU_group'] ?? 0,
                            'type' => $player['MU_type'] ?? 'embed',
                            'sound' => $player['MU_sound'] ?? $key,
                            'id' => $player['MU_id'] ?? null,
                            'original_lang' => $key
                        ];
                    }
                }
            } elseif (is_array($value)) {
                $this->extract_24hd_language_players_recursive($value, $language_players);
            }
        }
    }

    protected function process_24hd_language_video($post_id, $language, $players) {
        $this->log("Processing 24-HD $language video for post ID: $post_id with " . count($players) . " players");

        $total_players = count($players);
        $failed_players = 0;
        $http_errors = [];

        foreach ($players as $index => $player_data) {
            $player_url = $player_data['url'];
            $player_group = $player_data['group'];

            $this->log("Trying 24-HD $language player " . ($index + 1) . "/$total_players: $player_url (Group: $player_group)");

            $m3u8_files = $this->extract_24hd_m3u8_files($player_url);

            if ($m3u8_files === 'HTTP_500_ERROR') {
                $this->log("HTTP 500 Error from 24-HD $language player " . ($index + 1));
                $http_errors[] = "Player " . ($index + 1) . ": HTTP 500 Error";
                $failed_players++;
                continue;
            }

            if ($m3u8_files === 'HTTP_404_ERROR') {
                $this->log("HTTP 404 Error from 24-HD $language player " . ($index + 1));
                $http_errors[] = "Player " . ($index + 1) . ": HTTP 404 Error";
                $failed_players++;
                continue;
            }

            if (empty($m3u8_files)) {
                $this->log("No M3U8 files found for 24-HD $language player " . ($index + 1));
                $failed_players++;
                continue;
            }

            $this->log("Found " . count($m3u8_files) . " M3U8 files for 24-HD $language player " . ($index + 1));

            $saved_files = $this->save_m3u8_files($post_id, 'movie', $language, $m3u8_files);

            if ($saved_files) {
                $this->log("Successfully saved 24-HD $language M3U8 files from player " . ($index + 1));

                update_post_meta($post_id, "scraping_{$language}_status", 'completed');
                update_post_meta($post_id, "scraping_{$language}_message", 'Successfully processed');
                update_post_meta($post_id, "scraping_{$language}_files", $saved_files);
                update_post_meta($post_id, "scraping_{$language}_url", $player_url);
                update_post_meta($post_id, "scraping_{$language}_updated", current_time('mysql'));

                if (isset($m3u8_files['master_original']['url'])) {
                    update_post_meta($post_id, "{$language}_master_original_url", $m3u8_files['master_original']['url']);
                    $this->log("24-HD $language original master URL saved: " . $m3u8_files['master_original']['url']);
                }

                return $saved_files;
            } else {
                $this->log("Failed to save 24-HD $language M3U8 files from player " . ($index + 1));
                $failed_players++;
            }
        }

        $this->log("All 24-HD $language players failed. Total: $total_players, Failed: $failed_players");

        if (!empty($http_errors)) {
            $error_message = "Video sources unavailable: " . implode(", ", $http_errors);
            $this->log("HTTP errors detected for 24-HD $language: $error_message");
            update_post_meta($post_id, "scraping_{$language}_status", 'http_error');
            update_post_meta($post_id, "scraping_{$language}_message", $error_message);
            return 'HTTP_500_ERROR';
        } else {
            update_post_meta($post_id, "scraping_{$language}_status", 'failed');
            update_post_meta($post_id, "scraping_{$language}_message", 'No valid M3U8 files found from any player');
        }

        return false;
    }

    protected function extract_24hd_m3u8_files($player_url) {
        $this->log("Extracting 24-HD M3U8 files from: $player_url");
        
        $html = $this->fetch_html($player_url);
        
        if (!$html || $html === 'HTTP_500_ERROR' || $html === 'HTTP_404_ERROR') {
            $this->log("Failed to fetch 24-HD player HTML or HTTP error");
            return $html;
        }
        
        $patterns = [
            '/file:\s*["\']([^"\']+\.m3u8[^"\']*)["\']/',
            '/source:\s*["\']([^"\']+\.m3u8[^"\']*)["\']/',
            '/src:\s*["\']([^"\']+\.m3u8[^"\']*)["\']/',
            '/url:\s*["\']([^"\']+\.m3u8[^"\']*)["\']/',
            '/"([^"]*\.m3u8[^"]*)"/',
            '/' . "'([^']*\.m3u8[^']*)'/" . '',
            '/24hd\.com[^"]*\.m3u8[^"]*"/',
            '/player\.24hd\.com[^"]*\.m3u8[^"]*"/'
        ];
        
        $master_url = null;
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $master_url = $matches[1];
                $this->log("Found 24-HD M3U8 URL: $master_url");
                break;
            }
        }
        
        if (!$master_url) {
            $this->log("No 24-HD M3U8 source found");
            return false;
        }
        
        $master_content = $this->fetch_html($master_url);
        
        if (!$master_content || $master_content === 'HTTP_500_ERROR' || $master_content === 'HTTP_404_ERROR') {
            $this->log("Failed to fetch 24-HD master M3U8 content");
            return $master_content;
        }
        
        $files = [
            'master_original' => [
                'url' => $master_url,
                'content' => $master_content
            ]
        ];
        
        $this->extract_24hd_resolution_files($master_content, $master_url, $files);
        
        $this->log("Successfully extracted 24-HD M3U8 files: " . count($files) . " files");
        return $files;
    }

    protected function extract_24hd_resolution_files($master_content, $master_url, &$files) {
        $this->log("Extracting 24-HD resolution files from master M3U8");
        
        $lines = explode("\n", $master_content);
        
        if (strpos($master_url, '24hd.com') !== false) {
            $base_url = 'https://player.24hd.com/';
        } else {
            $base_url = dirname($master_url) . '/';
        }
        
        $resolutions_found = [];
        $current_resolution = null;
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            if (strpos($line, '#EXT-X-STREAM-INF:') === 0) {
                if (preg_match('/RESOLUTION=(\d+)x(\d+)/', $line, $matches)) {
                    $current_resolution = $matches[2];
                    $this->log("Found 24-HD resolution: {$current_resolution}p");
                }
            } elseif (!empty($line) && strpos($line, '#') !== 0 && strpos($line, '.m3u8') !== false && $current_resolution) {
                $resolutions_found[] = $current_resolution;
                
                if (strpos($line, 'http') === 0) {
                    $resolution_url = $line;
                } elseif (strpos($line, '//') === 0) {
                    $resolution_url = 'https:' . $line;
                } else {
                    $resolution_url = $base_url . $line;
                }
                
                $resolution_content = $this->fetch_html($resolution_url);
                
                if ($resolution_content) {
                    $files['resolutions'][$current_resolution] = [
                        'url' => $resolution_url,
                        'content' => $resolution_content,
                        'filename' => $current_resolution . '.m3u8'
                    ];
                    $this->log("Successfully extracted 24-HD resolution: {$current_resolution}p");
                }
                
                $current_resolution = null;
            }
        }
        
        if (empty($resolutions_found)) {
            $this->log("No 24-HD resolution files found in master M3U8");
        } else {
            $this->log("Total 24-HD resolutions extracted: " . count($resolutions_found));
        }
    }
}

class MovieScrapingBackgroundProcess extends WP_Background_Process {
    protected $action = 'movie_scraping_process';
    
    protected function task($item) {
        $post_id = $item['post_id'];
        $movie_url = $item['movie_url'];
        $options = $item['options'] ?? [];
        
        movie_scraper_log("Background processing movie scraping for post ID: $post_id, URL: $movie_url");
        
        $scraper = new MovieScraper();
        $result = $scraper->scrape($post_id, $movie_url, $options);
        
        if ($result === 'video_unavailable') {
            movie_scraper_log("Background process: Video unavailable for post ID: $post_id");
            return false;
        }
        
        if ($result) {
            movie_scraper_log("Background process: Successfully scraped movie for post ID: $post_id");
        } else {
            movie_scraper_log("Background process: Failed to scrape movie for post ID: $post_id");
        }
        
        return false;
    }
    
    protected function complete() {
        parent::complete();
        movie_scraper_log("Movie scraping background process completed");
    }
}

function init_movie_scraping_background_process() {
    global $movie_scraping_background_process;
    $movie_scraping_background_process = new MovieScrapingBackgroundProcess();
}
add_action('init', 'init_movie_scraping_background_process');

function queue_movie_scraping($post_id, $movie_url, $options = []) {
    global $movie_scraping_background_process;
    
    if (!$movie_scraping_background_process) {
        init_movie_scraping_background_process();
    }
    
    $movie_scraping_background_process->push_to_queue([
        'post_id' => $post_id,
        'movie_url' => $movie_url,
        'options' => $options
    ]);
    
    $movie_scraping_background_process->save()->dispatch();
}

function start_movie_scraping($post_id, $movie_url, $options = []) {
    $scraper = new MovieScraper();
    return $scraper->scrape($post_id, $movie_url, $options);
}

function test_movie_website_detection($url) {
    $scraper = new MovieScraper();
    $website = $scraper->detect_website($url);

    $supported_websites = [
        '22-hdd' => 'HD22MovieScraper',
        '24-hd' => 'HD24MovieScraper'
    ];

    return [
        'url' => $url,
        'detected_website' => $website,
        'scraper_class' => $supported_websites[$website] ?? 'ไม่รองรับ',
        'is_supported' => isset($supported_websites[$website])
    ];
}

if (!function_exists('movie_scraper_log')) {
    function movie_scraper_log($message) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('[Movie Scraper] ' . $message);
        }
    }
} 