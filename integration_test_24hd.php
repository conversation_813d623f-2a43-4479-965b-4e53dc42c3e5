<?php

class HD24IntegrationTester {
    private $testResults = [];
    private $outputDir = './24hd_integration_test';
    
    public function __construct() {
        $this->ensureOutputDir();
    }
    
    private function ensureOutputDir() {
        if (!is_dir($this->outputDir)) {
            mkdir($this->outputDir, 0755, true);
        }
    }
    
    private function log($message) {
        echo "[" . date('Y-m-d H:i:s') . "] $message\n";
    }
    
    private function saveToFile($filename, $content) {
        $filepath = $this->outputDir . '/' . $filename;
        file_put_contents($filepath, $content);
        $this->log("💾 Saved: $filepath (" . strlen($content) . " chars)");
    }
    
    public function testTitleExtraction($html, $expectedTitle = null) {
        $this->log("📝 Testing Title Extraction...");
        
        $patterns = [
            [
                'pattern' => '/<div[^>]*class=["\'][^"\']*movietext[^"\']*["\'][^>]*>[\s\S]*?<h1[^>]*>([^<]+)<\/h1>/i',
                'name' => 'movietext_h1',
                'priority' => 1
            ],
            [
                'pattern' => '/<h1[^>]*>([^<]+)<\/h1>/i',
                'name' => 'generic_h1',
                'priority' => 2
            ],
            [
                'pattern' => '/<meta[^>]*property=["\']og:title["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i',
                'name' => 'og_title',
                'priority' => 3
            ]
        ];
        
        foreach ($patterns as $pattern_info) {
            if (preg_match($pattern_info['pattern'], $html, $matches)) {
                $title = trim(strip_tags($matches[1]));
                $title = preg_replace('/^ดูหนัง\s*/i', '', $title);
                $title = preg_replace('/\s*เต็มเรื่อง.*$/i', '', $title);
                
                if (!empty($title) && strlen($title) > 3 &&
                    strpos($title, 'เว็บดูหนัง') === false && 
                    strpos($title, 'Netflix ฟรี') === false &&
                    strpos($title, 'ดูหนังใหม่') === false) {
                    
                    $this->log("✅ Title extracted: '$title' using pattern: {$pattern_info['name']}");
                    
                    if ($expectedTitle && $title === $expectedTitle) {
                        $this->log("✅ Title matches expected: '$expectedTitle'");
                    }
                    
                    return $title;
                }
            }
        }
        
        $this->log("❌ Title extraction failed");
        return null;
    }
    
    public function testIMDbRating($html, $expectedRating = null) {
        $this->log("⭐ Testing IMDb Rating Extraction...");
        
        $patterns = [
            [
                'pattern' => '/<span[^>]*class=["\']*score["\']*[^>]*>[\s\S]*?<\/noscript>([0-9]+\.?[0-9]*)/i',
                'name' => 'score_noscript',
                'priority' => 1
            ],
            [
                'pattern' => '/<span[^>]*class=["\']*score["\']*[^>]*>[\s\S]*?([0-9]+\.?[0-9]*)/i',
                'name' => 'score_span',
                'priority' => 2
            ]
        ];
        
        foreach ($patterns as $pattern_info) {
            if (preg_match($pattern_info['pattern'], $html, $matches)) {
                $rating = floatval($matches[1]);
                if ($rating >= 0 && $rating <= 10) {
                    $this->log("✅ IMDb rating extracted: $rating using pattern: {$pattern_info['name']}");
                    
                    if ($expectedRating && abs($rating - $expectedRating) < 0.1) {
                        $this->log("✅ Rating matches expected: $expectedRating");
                    }
                    
                    return $rating;
                }
            }
        }
        
        $this->log("❌ IMDb rating extraction failed");
        return null;
    }
    
    public function testYouTubeID($html, $expectedId = null) {
        $this->log("🎬 Testing YouTube ID Extraction...");
        
        $patterns = [
            [
                'pattern' => '/videoId:\s*["\']([a-zA-Z0-9_-]+)["\']/',
                'name' => 'videoId_js',
                'priority' => 1
            ],
            [
                'pattern' => '/youtube\.com\/vi\/([a-zA-Z0-9_-]+)/',
                'name' => 'youtube_vi',
                'priority' => 2
            ]
        ];
        
        foreach ($patterns as $pattern_info) {
            if (preg_match($pattern_info['pattern'], $html, $matches)) {
                $youtube_id = $matches[1];
                if (preg_match('/^[a-zA-Z0-9_-]{10,12}$/', $youtube_id)) {
                    $this->log("✅ YouTube ID extracted: $youtube_id using pattern: {$pattern_info['name']}");
                    
                    if ($expectedId && $youtube_id === $expectedId) {
                        $this->log("✅ YouTube ID matches expected: $expectedId");
                    }
                    
                    return $youtube_id;
                }
            }
        }
        
        $this->log("❌ YouTube ID extraction failed");
        return null;
    }
    
    public function testPlayerButtons($html) {
        $this->log("🎮 Testing Player Button Extraction...");
        
        $pattern = '/<span[^>]+class=["\']*halim-btn[^"\']*["\']*[^>]+data-post-id=["\']*([^"\']+)["\']*[^>]+data-server=["\']*([^"\']+)["\']*[^>]*>/gi';
        $matches = [];
        
        if (preg_match_all($pattern, $html, $allMatches, PREG_SET_ORDER)) {
            foreach ($allMatches as $match) {
                $matches[] = [
                    'postId' => $match[1],
                    'server' => $match[2]
                ];
            }
            
            $this->log("✅ Found " . count($matches) . " player buttons");
            foreach ($matches as $i => $btn) {
                $this->log("   " . ($i + 1) . ". Post ID: {$btn['postId']}, Server: {$btn['server']}");
            }
            
            return $matches;
        }
        
        $this->log("❌ Player button extraction failed");
        return [];
    }
    
    public function testPlayerURLGeneration($postId, $server = 1) {
        $this->log("🔗 Testing Player URL Generation for Post $postId, Server $server...");
        
        $possible_urls = [
            "https://main.24playerhd.com/index_th.php?id=" . substr(md5($postId . "1"), 0, 16) . "&b=" . $postId,
            "https://main.24playerhd.com/index_th.php?id=" . substr(md5($postId), 0, 16) . "&b=" . $postId,
            "https://main.24playerhd.com/index_th.php?id=" . $postId . "&server=" . $server
        ];
        
        foreach ($possible_urls as $url) {
            $this->log("   Testing: $url");
            
            if ($this->testURL($url)) {
                $this->log("✅ Working player URL found: $url");
                return $url;
            }
        }
        
        $this->log("❌ No working player URLs found");
        return null;
    }
    
    private function testURL($url) {
        if (!function_exists('curl_init')) {
            return false;
        }
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return ($http_code === 200 && $response && strlen($response) > 1000);
    }
    
    public function runComprehensiveTest($url, $expectedData = []) {
        $this->log("🚀 Starting comprehensive test for: $url");
        $this->log(str_repeat("=", 80));
        
        $testResult = [
            'url' => $url,
            'timestamp' => date('Y-m-d H:i:s'),
            'tests' => [],
            'success' => false,
            'errors' => []
        ];
        
        try {
            // Fetch HTML
            $this->log("📥 Fetching HTML...");
            $html = $this->fetchHTML($url);
            
            if (!$html) {
                throw new Exception("Failed to fetch HTML from $url");
            }
            
            $this->log("✅ HTML fetched successfully: " . strlen($html) . " characters");
            $this->saveToFile(basename(parse_url($url, PHP_URL_PATH)) . '_test.html', $html);
            
            // Test title extraction
            $title = $this->testTitleExtraction($html, $expectedData['title'] ?? null);
            $testResult['tests']['title'] = $title;
            
            // Test IMDb rating
            $rating = $this->testIMDbRating($html, $expectedData['rating'] ?? null);
            $testResult['tests']['rating'] = $rating;
            
            // Test YouTube ID
            $youtubeId = $this->testYouTubeID($html, $expectedData['youtubeId'] ?? null);
            $testResult['tests']['youtubeId'] = $youtubeId;
            
            // Test player buttons
            $playerButtons = $this->testPlayerButtons($html);
            $testResult['tests']['playerButtons'] = $playerButtons;
            
            // Test player URL generation
            if (!empty($playerButtons)) {
                $mainButton = array_filter($playerButtons, function($btn) { return $btn['server'] === '1'; });
                $mainButton = reset($mainButton) ?: $playerButtons[0];
                
                $playerUrl = $this->testPlayerURLGeneration($mainButton['postId'], $mainButton['server']);
                $testResult['tests']['playerUrl'] = $playerUrl;
            }
            
            // Calculate success rate
            $successCount = 0;
            $totalTests = 5;
            
            if ($title) $successCount++;
            if ($rating) $successCount++;
            if ($youtubeId) $successCount++;
            if (!empty($playerButtons)) $successCount++;
            if (isset($testResult['tests']['playerUrl']) && $testResult['tests']['playerUrl']) $successCount++;
            
            $testResult['successRate'] = round(($successCount / $totalTests) * 100);
            $testResult['success'] = $successCount >= 4; // At least 4 out of 5 tests should pass
            
            $this->log("📊 Test Results:");
            $this->log("   Success Rate: {$testResult['successRate']}% ($successCount/$totalTests)");
            $this->log("   Overall: " . ($testResult['success'] ? "✅ PASS" : "❌ FAIL"));
            
        } catch (Exception $e) {
            $this->log("❌ Test failed: " . $e->getMessage());
            $testResult['errors'][] = $e->getMessage();
        }
        
        $this->testResults[] = $testResult;
        $this->saveToFile('test_result_' . time() . '.json', json_encode($testResult, JSON_PRETTY_PRINT));
        
        return $testResult;
    }
    
    private function fetchHTML($url) {
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code === 200 && $response) {
                return $response;
            }
        }
        
        return false;
    }
    
    public function generateFinalReport() {
        $this->log("\n📊 FINAL INTEGRATION TEST REPORT");
        $this->log(str_repeat("=", 80));
        
        $totalTests = count($this->testResults);
        $passedTests = array_filter($this->testResults, function($result) { return $result['success']; });
        $passedCount = count($passedTests);
        
        $this->log("Total URLs tested: $totalTests");
        $this->log("Passed tests: $passedCount");
        $this->log("Failed tests: " . ($totalTests - $passedCount));
        $this->log("Overall success rate: " . round(($passedCount / $totalTests) * 100) . "%");
        
        $finalReport = [
            'timestamp' => date('Y-m-d H:i:s'),
            'summary' => [
                'totalTests' => $totalTests,
                'passedTests' => $passedCount,
                'failedTests' => $totalTests - $passedCount,
                'successRate' => round(($passedCount / $totalTests) * 100)
            ],
            'results' => $this->testResults
        ];
        
        $this->saveToFile('final_integration_report.json', json_encode($finalReport, JSON_PRETTY_PRINT));
        
        return $finalReport;
    }
}

// Run integration tests
$tester = new HD24IntegrationTester();

$testCases = [
    [
        'url' => 'https://www.24-hd.com/final-destination-4/',
        'expected' => [
            'title' => 'Final Destination 4 (2009) ไฟนอล เดสติเนชั่น 4 โกงตาย ทะลุตาย',
            'rating' => 5.1,
            'youtubeId' => 'yZFSkHTfH9Q'
        ]
    ],
    [
        'url' => 'https://www.24-hd.com/11-rebels/',
        'expected' => [
            'title' => '11 Rebels (2024)',
            'rating' => 6.3,
            'youtubeId' => '7-nUeY5z2Fs'
        ]
    ]
];

echo "🚀 Starting 24-HD.com Integration Tests...\n";
echo str_repeat("=", 100) . "\n";

foreach ($testCases as $i => $testCase) {
    echo "\n" . str_repeat("-", 80) . "\n";
    echo "🎬 TEST CASE " . ($i + 1) . ": " . $testCase['url'] . "\n";
    echo str_repeat("-", 80) . "\n";
    
    $result = $tester->runComprehensiveTest($testCase['url'], $testCase['expected']);
    
    echo "\n⏳ Waiting 3 seconds before next test...\n";
    sleep(3);
}

echo "\n" . str_repeat("=", 100) . "\n";
$finalReport = $tester->generateFinalReport();
echo "\n✅ Integration testing completed!\n";
?>
