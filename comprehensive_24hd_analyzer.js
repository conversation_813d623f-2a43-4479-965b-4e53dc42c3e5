const fs = require('fs');
const https = require('https');
const http = require('http');
const { URL } = require('url');
const path = require('path');
const { exec } = require('child_process');

class Comprehensive24HDAnalyzer {
    constructor() {
        this.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
        this.outputDir = './24hd_comprehensive_analysis';
        this.ensureOutputDir();
        this.testResults = {
            htmlFetch: false,
            titleExtraction: false,
            imdbRating: false,
            youtubeId: false,
            posterImage: false,
            playerButtons: false,
            playerUrls: [],
            errors: []
        };
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    async fetchWithCurl(url, method = 'GET', data = null) {
        return new Promise((resolve, reject) => {
            let curlCommand = `curl -s -L -H "User-Agent: ${this.userAgent}" -H "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8" -H "Accept-Language: th-TH,th;q=0.9,en;q=0.8" -H "Cache-Control: max-age=0"`;
            
            if (method === 'POST' && data) {
                curlCommand += ` -X POST -H "Content-Type: application/x-www-form-urlencoded" -d "${data}"`;
            }
            
            curlCommand += ` "${url}"`;
            
            exec(curlCommand, { maxBuffer: 1024 * 1024 * 10 }, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                    return;
                }
                
                resolve({
                    statusCode: 200,
                    headers: {},
                    body: stdout
                });
            });
        });
    }

    saveToFile(filename, content) {
        const filePath = path.join(this.outputDir, filename);
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`💾 Saved: ${filePath} (${content.length} chars)`);
        return filePath;
    }

    testTitleExtraction(html) {
        console.log('\n📝 Testing Title Extraction...');
        
        const patterns = [
            {
                name: 'movietext pattern',
                regex: /<div[^>]*class=["'][^"']*movietext[^"']*["'][^>]*>[\s\S]*?<h1[^>]*>([^<]+)<\/h1>/i
            },
            {
                name: 'h1 pattern',
                regex: /<h1[^>]*>([^<]+)<\/h1>/i
            },
            {
                name: 'og:title pattern',
                regex: /<meta[^>]*property=["']og:title["'][^>]*content=["']([^"']+)["'][^>]*>/i
            }
        ];

        for (const pattern of patterns) {
            const match = html.match(pattern.regex);
            if (match) {
                let title = match[1].trim();
                title = title.replace(/^ดูหนัง\s*/i, '').replace(/\s*เต็มเรื่อง.*$/i, '');
                
                if (title && title.length > 3 && 
                    !title.includes('เว็บดูหนัง') && 
                    !title.includes('Netflix ฟรี') &&
                    !title.includes('ดูหนังใหม่')) {
                    console.log(`✅ ${pattern.name}: "${title}"`);
                    this.testResults.titleExtraction = title;
                    return title;
                }
            }
        }
        
        console.log('❌ No valid title found');
        this.testResults.errors.push('Title extraction failed');
        return null;
    }

    testIMDbRating(html) {
        console.log('\n⭐ Testing IMDb Rating Extraction...');
        
        const patterns = [
            /<span[^>]*class=["']*score["']*[^>]*>[\s\S]*?<\/noscript>([0-9]+\.?[0-9]*)/i,
            /<span[^>]*class=["']*score["']*[^>]*>[\s\S]*?([0-9]+\.?[0-9]*)/i
        ];

        for (let i = 0; i < patterns.length; i++) {
            const match = html.match(patterns[i]);
            if (match) {
                const rating = parseFloat(match[1]);
                if (rating >= 0 && rating <= 10) {
                    console.log(`✅ Pattern ${i + 1}: ${rating}`);
                    this.testResults.imdbRating = rating;
                    return rating;
                }
            }
        }
        
        console.log('❌ No valid IMDb rating found');
        this.testResults.errors.push('IMDb rating extraction failed');
        return null;
    }

    testYouTubeID(html) {
        console.log('\n🎬 Testing YouTube ID Extraction...');
        
        const patterns = [
            /videoId:\s*['"]([a-zA-Z0-9_-]+)['"]/,
            /youtube\.com\/vi\/([a-zA-Z0-9_-]+)/g,
            /youtube\.com\/embed\/([a-zA-Z0-9_-]+)/g
        ];

        for (let i = 0; i < patterns.length; i++) {
            const match = html.match(patterns[i]);
            if (match) {
                const youtubeId = match[1];
                if (youtubeId && youtubeId.length >= 10) {
                    console.log(`✅ Pattern ${i + 1}: ${youtubeId}`);
                    this.testResults.youtubeId = youtubeId;
                    return youtubeId;
                }
            }
        }
        
        console.log('❌ No valid YouTube ID found');
        this.testResults.errors.push('YouTube ID extraction failed');
        return null;
    }

    testPosterImage(html) {
        console.log('\n🖼️ Testing Poster Image Extraction...');
        
        const pattern = /<meta[^>]*property=["']og:image["'][^>]*content=["']([^"']+)["'][^>]*>/i;
        const match = html.match(pattern);
        
        if (match) {
            const posterUrl = match[1].trim();
            console.log(`✅ Found poster: ${posterUrl}`);
            this.testResults.posterImage = posterUrl;
            return posterUrl;
        }
        
        console.log('❌ No poster image found');
        this.testResults.errors.push('Poster image extraction failed');
        return null;
    }

    testPlayerButtons(html) {
        console.log('\n🎮 Testing Player Button Extraction...');
        
        const pattern = /<span[^>]+class=["']*halim-btn[^"']*["']*[^>]+data-post-id=["']*([^"']+)["']*[^>]+data-server=["']*([^"']+)["']*[^>]*>/gi;
        const matches = [];
        let match;
        
        while ((match = pattern.exec(html)) !== null) {
            const postId = match[1];
            const server = match[2];
            matches.push({ postId, server });
        }
        
        if (matches.length > 0) {
            console.log(`✅ Found ${matches.length} player buttons:`);
            matches.forEach((btn, i) => {
                console.log(`   ${i + 1}. Post ID: ${btn.postId}, Server: ${btn.server}`);
            });
            this.testResults.playerButtons = matches;
            return matches;
        }
        
        console.log('❌ No player buttons found');
        this.testResults.errors.push('Player button extraction failed');
        return [];
    }

    async testPlayerURLGeneration(postId, server) {
        console.log(`\n🔗 Testing Player URL Generation for Post ${postId}, Server ${server}...`);
        
        const possibleUrls = [
            `https://main.24playerhd.com/index_th.php?id=${this.generateHash(postId, server)}&b=${postId}`,
            `https://main.24playerhd.com/index_th.php?id=${postId}&server=${server}`,
            `https://player.24-hd.com/embed.php?id=${postId}&server=${server}`,
            `https://embed.24-hd.com/player.php?post=${postId}&server=${server}`
        ];

        for (const url of possibleUrls) {
            try {
                console.log(`   Testing: ${url}`);
                const response = await this.fetchWithCurl(url);
                
                if (response.body && response.body.length > 1000 && 
                    !response.body.includes('error') && 
                    !response.body.includes('404')) {
                    console.log(`   ✅ Working URL found: ${url}`);
                    this.testResults.playerUrls.push(url);
                    return url;
                }
            } catch (error) {
                console.log(`   ❌ Failed: ${error.message}`);
            }
        }
        
        console.log('   ❌ No working player URLs found');
        return null;
    }

    generateHash(postId, server) {
        const crypto = require('crypto');
        return crypto.createHash('md5').update(`${postId}${server}`).digest('hex').substring(0, 16);
    }

    async comprehensiveAnalysis(url) {
        console.log(`🚀 Starting comprehensive analysis of: ${url}`);
        console.log('='.repeat(80));
        
        try {
            // Step 1: Fetch HTML
            console.log('\n📥 Step 1: Fetching HTML...');
            const response = await this.fetchWithCurl(url);
            
            if (!response.body || response.body.length < 1000) {
                throw new Error('Failed to fetch valid HTML content');
            }
            
            console.log(`✅ HTML fetched successfully: ${response.body.length} characters`);
            this.testResults.htmlFetch = true;
            this.saveToFile('main_page.html', response.body);
            
            const html = response.body;
            
            // Step 2: Test all extraction methods
            const title = this.testTitleExtraction(html);
            const imdbRating = this.testIMDbRating(html);
            const youtubeId = this.testYouTubeID(html);
            const posterImage = this.testPosterImage(html);
            const playerButtons = this.testPlayerButtons(html);
            
            // Step 3: Test player URL generation
            if (playerButtons.length > 0) {
                const mainButton = playerButtons.find(btn => btn.server === '1') || playerButtons[0];
                await this.testPlayerURLGeneration(mainButton.postId, mainButton.server);
            }
            
            // Step 4: Generate comprehensive report
            this.generateReport(url);
            
            return this.testResults;
            
        } catch (error) {
            console.error(`❌ Analysis failed: ${error.message}`);
            this.testResults.errors.push(error.message);
            return this.testResults;
        }
    }

    generateReport(url) {
        console.log('\n📊 COMPREHENSIVE ANALYSIS REPORT');
        console.log('='.repeat(80));
        
        const report = {
            url,
            timestamp: new Date().toISOString(),
            testResults: this.testResults,
            summary: {
                totalTests: 6,
                passedTests: 0,
                failedTests: 0,
                successRate: 0
            }
        };
        
        // Calculate success rate
        const tests = [
            this.testResults.htmlFetch,
            this.testResults.titleExtraction,
            this.testResults.imdbRating,
            this.testResults.youtubeId,
            this.testResults.posterImage,
            this.testResults.playerButtons
        ];
        
        report.summary.passedTests = tests.filter(test => test).length;
        report.summary.failedTests = tests.filter(test => !test).length;
        report.summary.successRate = Math.round((report.summary.passedTests / report.summary.totalTests) * 100);
        
        console.log(`📈 Success Rate: ${report.summary.successRate}% (${report.summary.passedTests}/${report.summary.totalTests})`);
        console.log(`✅ Passed: ${report.summary.passedTests}`);
        console.log(`❌ Failed: ${report.summary.failedTests}`);
        
        if (this.testResults.errors.length > 0) {
            console.log(`\n🚨 Errors (${this.testResults.errors.length}):`);
            this.testResults.errors.forEach((error, i) => {
                console.log(`   ${i + 1}. ${error}`);
            });
        }
        
        this.saveToFile('comprehensive_report.json', JSON.stringify(report, null, 2));
        console.log('\n✅ Comprehensive analysis completed!');
    }
}

// Run analysis
const analyzer = new Comprehensive24HDAnalyzer();
const testUrls = [
    'https://www.24-hd.com/final-destination-4/',
    'https://www.24-hd.com/11-rebels/'
];

async function runTests() {
    for (const url of testUrls) {
        console.log(`\n${'='.repeat(100)}`);
        console.log(`🎬 TESTING: ${url}`);
        console.log(`${'='.repeat(100)}`);
        
        await analyzer.comprehensiveAnalysis(url);
        
        console.log('\n⏳ Waiting 3 seconds before next test...');
        await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    console.log('\n🎉 All tests completed!');
}

runTests().catch(console.error);
