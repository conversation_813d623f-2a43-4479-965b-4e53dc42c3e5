<?php

echo "🚀 Testing AJAX player extraction...\n";

function test_ajax_request($post_id, $server) {
    $ajax_url = 'https://www.24-hd.com/wp-admin/admin-ajax.php';
    $form_data = "action=doo_player_ajax&post=$post_id&nume=$server&type=movie";
    
    echo "📡 Testing AJAX request:\n";
    echo "   URL: $ajax_url\n";
    echo "   Data: $form_data\n";
    
    if (function_exists('curl_init')) {
        echo "   Method: cURL\n";
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $ajax_url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $form_data,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/x-www-form-urlencoded',
                'X-Requested-With: XMLHttpRequest',
                'Accept: application/json, text/javascript, */*; q=0.01',
                'Accept-Language: th-TH,th;q=0.9,en;q=0.8',
                'Referer: https://www.24-hd.com/',
                'Origin: https://www.24-hd.com',
                'Cache-Control: no-cache'
            ],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "   HTTP Code: $http_code\n";
        echo "   Response Length: " . strlen($response) . " chars\n";
        
        if (!empty($error)) {
            echo "   ❌ cURL Error: $error\n";
            return false;
        }
        
        if ($http_code !== 200) {
            echo "   ❌ HTTP Error: $http_code\n";
            return false;
        }
        
        // Save response for analysis
        file_put_contents("ajax_test_response_{$post_id}_{$server}.html", $response);
        echo "   💾 Response saved to: ajax_test_response_{$post_id}_{$server}.html\n";
        
        // Check for Cloudflare
        if (strpos($response, 'blocked') !== false || strpos($response, 'Cloudflare') !== false) {
            echo "   🛡️ Cloudflare protection detected\n";
            return false;
        }
        
        // Try JSON decode
        $data = json_decode($response, true);
        if ($data && isset($data['embed_url'])) {
            echo "   ✅ JSON response with embed_url: " . $data['embed_url'] . "\n";
            return $data['embed_url'];
        }
        
        // Try iframe patterns
        $iframe_patterns = [
            '/<iframe[^>]+src=["\']*([^"\']+)["\']*[^>]*>/i',
            '/src=["\']*([^"\']*24playerhd\.com[^"\']*)["\']*/i',
            '/src=["\']*([^"\']*main\.24[^"\']*)["\']*/i'
        ];
        
        foreach ($iframe_patterns as $i => $pattern) {
            if (preg_match($pattern, $response, $matches)) {
                $src = $matches[1];
                echo "   ✅ Pattern " . ($i + 1) . " found iframe: $src\n";
                return $src;
            }
        }
        
        echo "   ⚠️ No player URL found in response\n";
        echo "   Response preview: " . substr($response, 0, 200) . "...\n";
        return false;
        
    } else {
        echo "   ❌ cURL not available\n";
        return false;
    }
}

// Test with 11 Rebels data
echo "\n🎬 Testing with 11 Rebels (Post ID: 35854, Server: 1)...\n";
$result1 = test_ajax_request(35854, 1);

echo "\n🎬 Testing with Final Destination 4 (Post ID: 29814, Server: 1)...\n";
$result2 = test_ajax_request(29814, 1);

echo "\n📊 Summary:\n";
echo "11 Rebels result: " . ($result1 ? $result1 : "Failed") . "\n";
echo "Final Destination 4 result: " . ($result2 ? $result2 : "Failed") . "\n";

echo "\n✅ AJAX testing completed!\n";
?>
