<?php

echo "🚀 Simple Final Test for 24-HD.com System\n";
echo str_repeat("=", 60) . "\n";

function testPattern($pattern, $html, $testName) {
    echo "Testing $testName... ";
    if (preg_match($pattern, $html, $matches)) {
        echo "✅ PASS\n";
        return true;
    } else {
        echo "❌ FAIL\n";
        return false;
    }
}

// Check if HTML file exists
$htmlFile = '24hd_comprehensive_analysis/main_page.html';
if (!file_exists($htmlFile)) {
    echo "❌ HTML file not found: $htmlFile\n";
    echo "Please run the comprehensive analyzer first.\n";
    exit(1);
}

$html = file_get_contents($htmlFile);
echo "📄 HTML loaded: " . strlen($html) . " characters\n\n";

// Test all patterns
$tests = [
    'Title' => '/<div[^>]*class=["\'][^"\']*movietext[^"\']*["\'][^>]*>[\s\S]*?<h1[^>]*>([^<]+)<\/h1>/i',
    'IMDb Rating' => '/<span[^>]*class=["\']*score["\']*[^>]*>[\s\S]*?<\/noscript>([0-9]+\.?[0-9]*)/i',
    'YouTube ID' => '/videoId:\s*["\']([a-zA-Z0-9_-]+)["\']/',
    'Poster Image' => '/<meta[^>]*property=["\']*og:image["\']*[^>]*content=["\']*([^"\']+)["\']*[^>]*>/i',
    'Player Buttons' => '/<span[^>]+class=["\']*halim-btn[^"\']*["\']*[^>]+data-post-id=["\']*([^"\']+)["\']*[^>]+data-server=["\']*([^"\']+)["\']*[^>]*>/i'
];

$results = [];
foreach ($tests as $testName => $pattern) {
    $results[$testName] = testPattern($pattern, $html, $testName);
}

// Calculate success rate
$passed = array_filter($results);
$successRate = round((count($passed) / count($results)) * 100);

echo "\n" . str_repeat("-", 60) . "\n";
echo "📊 FINAL RESULTS:\n";
echo "   Passed: " . count($passed) . "/" . count($results) . "\n";
echo "   Success Rate: {$successRate}%\n";

if ($successRate >= 80) {
    echo "   Status: ✅ READY FOR PRODUCTION\n";
    echo "\n🎉 24-HD.com scraper is working correctly!\n";
    echo "All critical patterns are functional.\n";
} else {
    echo "   Status: ❌ NEEDS FIXES\n";
    echo "\n⚠️ Some patterns need adjustment.\n";
}

// Test file structure
echo "\n📁 Testing file structure...\n";
$requiredFiles = [
    'includes/scrapers/BaseScraper.php',
    'includes/scrapers/MovieScraper.php'
];

$fileTests = [];
foreach ($requiredFiles as $file) {
    echo "Checking $file... ";
    if (file_exists($file)) {
        echo "✅ EXISTS\n";
        $fileTests[] = true;
    } else {
        echo "❌ MISSING\n";
        $fileTests[] = false;
    }
}

$fileSuccessRate = round((array_sum($fileTests) / count($fileTests)) * 100);
echo "File structure: {$fileSuccessRate}%\n";

// Overall assessment
$overallSuccess = ($successRate + $fileSuccessRate) / 2;
echo "\n" . str_repeat("=", 60) . "\n";
echo "🎯 OVERALL ASSESSMENT: " . round($overallSuccess) . "%\n";

if ($overallSuccess >= 80) {
    echo "✅ SYSTEM IS READY FOR PRODUCTION!\n";
    echo "The 24-HD.com scraper has been successfully implemented.\n";
} else {
    echo "⚠️ SYSTEM NEEDS MORE WORK\n";
    echo "Please address the failing tests before production.\n";
}

echo "\n✅ Simple final test completed!\n";
?>
