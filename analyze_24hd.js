const fs = require('fs');
const https = require('https');
const http = require('http');
const { URL } = require('url');
const path = require('path');
const { exec } = require('child_process');

class HD24Analyzer {
    constructor() {
        this.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
        this.outputDir = './24hd_analysis';
        this.ensureOutputDir();
    }

    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    async fetchWithCloudflare(url, retries = 3) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const options = {
                hostname: urlObj.hostname,
                port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
                path: urlObj.pathname + urlObj.search,
                method: 'GET',
                headers: {
                    'User-Agent': this.userAgent,
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/avif,image/jxl,image/heic,image/heic-sequence,*/*;q=0.8',
                    'Accept-Language': 'th-TH,th;q=0.9,en-US;q=0.8,en;q=0.7',
                    'Accept-Encoding': 'gzip, deflate, br, zstd',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Cache-Control': 'max-age=0',
                    'DNT': '1',
                    'Sec-GPC': '1',
                    'Priority': 'u=0, i'
                }
            };

            const client = urlObj.protocol === 'https:' ? https : http;

            const req = client.request(options, (res) => {
                if (res.statusCode === 403 && retries > 0) {
                    console.log(`🔄 Cloudflare protection detected, retrying... (${retries} attempts left)`);
                    setTimeout(() => {
                        this.fetchWithCloudflare(url, retries - 1).then(resolve).catch(reject);
                    }, 3000);
                    return;
                }

                if (res.statusCode === 301 || res.statusCode === 302) {
                    const redirectUrl = res.headers.location;
                    console.log(`🔄 Redirecting to: ${redirectUrl}`);
                    this.fetchWithCloudflare(redirectUrl, retries).then(resolve).catch(reject);
                    return;
                }

                let data = '';

                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    console.log(`✅ Fetched ${url} - Status: ${res.statusCode}, Size: ${data.length} chars`);

                    if (res.statusCode === 403 && data.includes('cloudflare')) {
                        console.log(`🛡️ Cloudflare challenge detected, saving response for analysis`);
                        this.saveToFile('cloudflare_challenge.html', data);
                    }

                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: data
                    });
                });
            });

            req.on('error', (err) => {
                console.error(`❌ Error fetching ${url}:`, err.message);
                if (retries > 0) {
                    console.log(`🔄 Retrying... (${retries} attempts left)`);
                    setTimeout(() => {
                        this.fetchWithCloudflare(url, retries - 1).then(resolve).catch(reject);
                    }, 2000);
                } else {
                    reject(err);
                }
            });

            req.setTimeout(45000, () => {
                req.destroy();
                if (retries > 0) {
                    console.log(`⏰ Timeout, retrying... (${retries} attempts left)`);
                    this.fetchWithCloudflare(url, retries - 1).then(resolve).catch(reject);
                } else {
                    reject(new Error('Request timeout'));
                }
            });

            req.end();
        });
    }

    async fetchWithCurl(url, method = 'GET', data = null) {
        return new Promise((resolve, reject) => {
            let curlCommand = `curl -s -L -H "User-Agent: ${this.userAgent}" -H "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8" -H "Accept-Language: th-TH,th;q=0.9,en;q=0.8" -H "Cache-Control: max-age=0"`;

            if (method === 'POST' && data) {
                curlCommand += ` -X POST -H "Content-Type: application/x-www-form-urlencoded" -d "${data}"`;
            }

            curlCommand += ` "${url}"`;

            console.log(`🌐 Using curl to ${method}: ${url}`);

            exec(curlCommand, { maxBuffer: 1024 * 1024 * 10 }, (error, stdout, stderr) => {
                if (error) {
                    console.error(`❌ Curl error: ${error.message}`);
                    reject(error);
                    return;
                }

                if (stderr) {
                    console.log(`⚠️ Curl stderr: ${stderr}`);
                }

                console.log(`✅ Curl fetched ${url} - Size: ${stdout.length} chars`);
                resolve({
                    statusCode: 200,
                    headers: {},
                    body: stdout
                });
            });
        });
    }

    saveToFile(filename, content) {
        const filePath = path.join(this.outputDir, filename);
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`💾 Saved: ${filePath} (${content.length} chars)`);
        return filePath;
    }

    extractTitle(html) {
        const patterns = [
            /<h1[^>]*>([^<]+)<\/h1>/i,
            /<meta[^>]*property=["']og:title["'][^>]*content=["']([^"']+)["'][^>]*>/i,
            /<title[^>]*>ดูหนัง\s*([^<]+?)\s*เต็มเรื่อง[^<]*<\/title>/i,
            /<title[^>]*>([^<]+)<\/title>/i
        ];

        for (const pattern of patterns) {
            const match = html.match(pattern);
            if (match) {
                let title = match[1].trim();
                if (title && !title.includes('เว็บดูหนัง') && !title.includes('Netflix')) {
                    return title;
                }
            }
        }
        return null;
    }

    extractIMDbRating(html) {
        const patterns = [
            /<span[^>]*class=["'][^"']*score[^"']*["'][^>]*>[\s\S]*?(\d+\.?\d*)/i,
            /<span[^>]*class=["']score["'][^>]*>[\s\S]*?(\d+\.?\d*)/i,
            /IMDb[^>]*>[\s\S]*?(\d+\.?\d*)/i,
            /<img[^>]*alt="[^"]*IMDb"[^>]*><\/noscript>(\d+\.?\d*)/i
        ];

        for (const pattern of patterns) {
            const match = html.match(pattern);
            if (match) {
                const rating = parseFloat(match[1]);
                if (rating >= 0 && rating <= 10) {
                    return rating;
                }
            }
        }

        const scoreMatch = html.match(/<span[^>]*class=["']score["'][^>]*>[\s\S]*?<\/noscript>(\d+\.?\d*)/i);
        if (scoreMatch) {
            const rating = parseFloat(scoreMatch[1]);
            if (rating >= 0 && rating <= 10) {
                return rating;
            }
        }

        return null;
    }

    extractYouTubeID(html) {
        const patterns = [
            /videoId:\s*['"]([a-zA-Z0-9_-]+)['"]/,
            /youtube\.com\/vi\/([a-zA-Z0-9_-]+)/g,
            /youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/g,
            /youtu\.be\/([a-zA-Z0-9_-]+)/g,
            /youtube\.com\/embed\/([a-zA-Z0-9_-]+)/g,
            /youtube\.com\/v\/([a-zA-Z0-9_-]+)/g
        ];

        for (const pattern of patterns) {
            const match = html.match(pattern);
            if (match) {
                return match[1];
            }
        }
        return null;
    }

    extractPosterImage(html) {
        const patterns = [
            /<meta[^>]*property=["']og:image["'][^>]*content=["']([^"']+)["'][^>]*>/i,
            /<img[^>]*class=["'][^"']*poster[^"']*["'][^>]*src=["']([^"']+)["'][^>]*>/i,
            /<img[^>]*src=["']([^"']+)["'][^>]*class=["'][^"']*poster[^"']*["'][^>]*>/i
        ];

        for (const pattern of patterns) {
            const match = html.match(pattern);
            if (match) {
                return match[1];
            }
        }
        return null;
    }

    extractMainPlayerIframes(html) {
        const patterns = [
            /<iframe[^>]+src=["']([^"']*24playerhd\.com[^"']*)["'][^>]*>/gi,
            /<iframe[^>]+src=["']([^"']*main\.24[^"']*)["'][^>]*>/gi,
            /<iframe[^>]+src=["']([^"']*player[^"']*)["'][^>]*>/gi
        ];

        const iframes = [];

        for (const pattern of patterns) {
            let match;
            while ((match = pattern.exec(html)) !== null) {
                const src = match[1];
                if (!iframes.includes(src)) {
                    iframes.push(src);
                }
            }
        }

        return iframes;
    }

    extractPlayerData(html) {
        const playerButtons = [];
        const pattern = /<span[^>]+class=["'][^"']*halim-btn[^"']*["'][^>]+data-post-id=["']([^"']+)["'][^>]+data-server=["']([^"']+)["'][^>]*>/gi;

        let match;
        while ((match = pattern.exec(html)) !== null) {
            const postId = match[1];
            const server = match[2];

            const buttonText = html.substring(match.index, html.indexOf('</span>', match.index));
            const isMainPlayer = buttonText.includes('ตัวเล่นหลัก') || buttonText.includes('main');

            playerButtons.push({
                postId,
                server,
                isMainPlayer,
                buttonText: buttonText.replace(/<[^>]*>/g, '').trim()
            });
        }

        return playerButtons;
    }

    async fetchPlayerAjax(postId, server, refererUrl) {
        const ajaxUrl = 'https://www.24-hd.com/wp-admin/admin-ajax.php';
        const formData = `action=doo_player_ajax&post=${postId}&nume=${server}&type=movie`;

        console.log(`🎬 Fetching player via AJAX: Post ID ${postId}, Server ${server}`);

        try {
            let curlCommand = `curl -s -L -X POST`;
            curlCommand += ` -H "User-Agent: ${this.userAgent}"`;
            curlCommand += ` -H "Accept: application/json, text/javascript, */*; q=0.01"`;
            curlCommand += ` -H "Accept-Language: th-TH,th;q=0.9,en;q=0.8"`;
            curlCommand += ` -H "Content-Type: application/x-www-form-urlencoded; charset=UTF-8"`;
            curlCommand += ` -H "X-Requested-With: XMLHttpRequest"`;
            curlCommand += ` -H "Referer: ${refererUrl}"`;
            curlCommand += ` -H "Origin: https://www.24-hd.com"`;
            curlCommand += ` -d "${formData}"`;
            curlCommand += ` "${ajaxUrl}"`;

            const response = await new Promise((resolve, reject) => {
                exec(curlCommand, { maxBuffer: 1024 * 1024 * 10 }, (error, stdout, stderr) => {
                    if (error) {
                        reject(error);
                        return;
                    }
                    resolve({
                        statusCode: 200,
                        body: stdout
                    });
                });
            });

            if (response.statusCode === 200 && response.body) {
                this.saveToFile(`ajax_response_${postId}_${server}.html`, response.body);

                if (response.body.includes('blocked') || response.body.includes('Cloudflare')) {
                    console.log(`🛡️ AJAX request blocked by Cloudflare`);
                    return null;
                }

                try {
                    const data = JSON.parse(response.body);
                    if (data && data.embed_url) {
                        console.log(`✅ Got player URL: ${data.embed_url}`);
                        return data.embed_url;
                    }
                } catch (jsonError) {
                    console.log(`⚠️ AJAX response is not JSON, checking for iframe in HTML...`);

                    const iframePatterns = [
                        /<iframe[^>]+src=["']([^"']+)["'][^>]*>/gi,
                        /src=["']([^"']*24playerhd\.com[^"']*)["']/gi,
                        /src=["']([^"']*main\.24[^"']*)["']/gi
                    ];

                    for (const pattern of iframePatterns) {
                        const match = response.body.match(pattern);
                        if (match) {
                            const src = match[1] || match[0].match(/src=["']([^"']+)["']/)[1];
                            console.log(`✅ Found iframe URL in HTML: ${src}`);
                            return src;
                        }
                    }
                }
            }

            console.log(`⚠️ No player URL found in AJAX response`);
            return null;

        } catch (error) {
            console.error(`❌ Error fetching player AJAX:`, error.message);
            return null;
        }
    }

    async analyzePlayerPage(playerUrl) {
        try {
            console.log(`🔍 Analyzing player: ${playerUrl}`);
            const response = await this.fetchWithCloudflare(playerUrl);
            
            if (response.statusCode !== 200) {
                console.log(`⚠️ Player returned status: ${response.statusCode}`);
                return null;
            }

            const playerHtml = response.body;
            this.saveToFile(`player_${Date.now()}.html`, playerHtml);

            const m3u8Patterns = [
                /file:\s*["']([^"']+\.m3u8[^"']*)["']/g,
                /source:\s*["']([^"']+\.m3u8[^"']*)["']/g,
                /src:\s*["']([^"']+\.m3u8[^"']*)["']/g,
                /url:\s*["']([^"']+\.m3u8[^"']*)["']/g,
                /["']([^"']*\.m3u8[^"']*)["']/g
            ];

            const m3u8Urls = [];
            
            for (const pattern of m3u8Patterns) {
                let match;
                while ((match = pattern.exec(playerHtml)) !== null) {
                    const url = match[1];
                    if (!m3u8Urls.includes(url)) {
                        m3u8Urls.push(url);
                    }
                }
            }

            return {
                playerUrl,
                m3u8Urls,
                htmlSize: playerHtml.length
            };

        } catch (error) {
            console.error(`❌ Error analyzing player ${playerUrl}:`, error.message);
            return null;
        }
    }

    async analyzeM3U8(m3u8Url) {
        try {
            console.log(`📺 Analyzing M3U8: ${m3u8Url}`);
            const response = await this.fetchWithCloudflare(m3u8Url);
            
            if (response.statusCode !== 200) {
                console.log(`⚠️ M3U8 returned status: ${response.statusCode}`);
                return null;
            }

            const m3u8Content = response.body;
            this.saveToFile(`m3u8_${Date.now()}.m3u8`, m3u8Content);

            const isMaster = m3u8Content.includes('#EXT-X-STREAM-INF:');
            const hasSegments = m3u8Content.includes('#EXTINF:');
            
            let duration = 0;
            if (hasSegments) {
                const extinf = m3u8Content.match(/#EXTINF:(\d+\.?\d*)/g);
                if (extinf) {
                    duration = extinf.reduce((total, line) => {
                        const match = line.match(/#EXTINF:(\d+\.?\d*)/);
                        return total + (match ? parseFloat(match[1]) : 0);
                    }, 0);
                }
            }

            return {
                url: m3u8Url,
                isMaster,
                hasSegments,
                duration: Math.round(duration / 60),
                contentSize: m3u8Content.length,
                content: m3u8Content
            };

        } catch (error) {
            console.error(`❌ Error analyzing M3U8 ${m3u8Url}:`, error.message);
            return null;
        }
    }

    async analyze(url) {
        console.log(`🚀 Starting analysis of: ${url}`);
        
        try {
            let response = await this.fetchWithCloudflare(url);

            if (response.statusCode === 403) {
                console.log(`🛡️ Cloudflare protection detected, trying curl method...`);
                response = await this.fetchWithCurl(url);
            }

            if (response.statusCode !== 200 && !response.body) {
                console.log(`❌ Main page returned status: ${response.statusCode}`);
                return;
            }

            const html = response.body;
            this.saveToFile('main_page.html', html);

            console.log('\n📊 ANALYSIS RESULTS:');
            console.log('='.repeat(50));

            const title = this.extractTitle(html);
            console.log(`📝 Title: ${title || 'Not found'}`);

            const imdbRating = this.extractIMDbRating(html);
            console.log(`⭐ IMDb Rating: ${imdbRating || 'Not found'}`);

            const youtubeId = this.extractYouTubeID(html);
            console.log(`🎬 YouTube ID: ${youtubeId || 'Not found'}`);

            const posterImage = this.extractPosterImage(html);
            console.log(`🖼️ Poster: ${posterImage || 'Not found'}`);

            const iframes = this.extractMainPlayerIframes(html);
            console.log(`🎥 Player iframes found: ${iframes.length}`);

            for (let i = 0; i < iframes.length; i++) {
                console.log(`   ${i + 1}. ${iframes[i]}`);
            }

            const playerButtons = this.extractPlayerData(html);
            console.log(`🎮 Player buttons found: ${playerButtons.length}`);

            for (let i = 0; i < playerButtons.length; i++) {
                const button = playerButtons[i];
                console.log(`   ${i + 1}. ${button.buttonText} (Post: ${button.postId}, Server: ${button.server})`);
            }

            if (playerButtons.length > 0) {
                console.log('\n🔍 Analyzing AJAX players...');

                for (const button of playerButtons) {
                    if (button.isMainPlayer || button.server === '1') {
                        const playerUrl = await this.fetchPlayerAjax(button.postId, button.server, url);

                        if (playerUrl) {
                            console.log(`\n📺 Found player URL: ${playerUrl}`);

                            const playerAnalysis = await this.analyzePlayerPage(playerUrl);
                            if (playerAnalysis && playerAnalysis.m3u8Urls.length > 0) {
                                console.log(`   M3U8 URLs found: ${playerAnalysis.m3u8Urls.length}`);

                                for (const m3u8Url of playerAnalysis.m3u8Urls) {
                                    const m3u8Analysis = await this.analyzeM3U8(m3u8Url);
                                    if (m3u8Analysis) {
                                        console.log(`   📊 ${m3u8Url}`);
                                        console.log(`      Type: ${m3u8Analysis.isMaster ? 'Master' : 'Segments'}`);
                                        console.log(`      Duration: ${m3u8Analysis.duration} minutes`);
                                        console.log(`      Size: ${m3u8Analysis.contentSize} bytes`);
                                    }
                                }
                            }
                        }

                        await new Promise(resolve => setTimeout(resolve, 3000));
                    }
                }
            }

            if (iframes.length > 0) {
                console.log('\n🔍 Analyzing static iframes...');

                for (const iframe of iframes) {
                    const playerAnalysis = await this.analyzePlayerPage(iframe);
                    if (playerAnalysis && playerAnalysis.m3u8Urls.length > 0) {
                        console.log(`\n📺 Player: ${iframe}`);
                        console.log(`   M3U8 URLs found: ${playerAnalysis.m3u8Urls.length}`);

                        for (const m3u8Url of playerAnalysis.m3u8Urls) {
                            const m3u8Analysis = await this.analyzeM3U8(m3u8Url);
                            if (m3u8Analysis) {
                                console.log(`   📊 ${m3u8Url}`);
                                console.log(`      Type: ${m3u8Analysis.isMaster ? 'Master' : 'Segments'}`);
                                console.log(`      Duration: ${m3u8Analysis.duration} minutes`);
                                console.log(`      Size: ${m3u8Analysis.contentSize} bytes`);
                            }
                        }
                    }

                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }

            const summary = {
                url,
                title,
                imdbRating,
                youtubeId,
                posterImage,
                iframes,
                playerButtons,
                timestamp: new Date().toISOString()
            };

            this.saveToFile('analysis_summary.json', JSON.stringify(summary, null, 2));
            console.log('\n✅ Analysis complete! Check the 24hd_analysis folder for saved files.');

        } catch (error) {
            console.error('❌ Analysis failed:', error.message);
        }
    }
}

const analyzer = new HD24Analyzer();
const targetUrl = process.argv[2] || 'https://www.24-hd.com/final-destination-4/';

analyzer.analyze(targetUrl);
