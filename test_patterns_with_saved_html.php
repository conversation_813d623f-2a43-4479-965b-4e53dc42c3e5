<?php

class PatternTester {
    private $testResults = [];
    
    public function __construct() {
        echo "🚀 Testing 24-HD.com Patterns with Saved HTML...\n";
        echo str_repeat("=", 80) . "\n";
    }
    
    private function log($message) {
        echo "[" . date('H:i:s') . "] $message\n";
    }
    
    public function testTitleExtraction($html, $expectedTitle = null) {
        $this->log("📝 Testing Title Extraction...");
        
        $patterns = [
            [
                'pattern' => '/<div[^>]*class=["\'][^"\']*movietext[^"\']*["\'][^>]*>[\s\S]*?<h1[^>]*>([^<]+)<\/h1>/i',
                'name' => 'movietext_h1',
                'priority' => 1
            ],
            [
                'pattern' => '/<h1[^>]*>([^<]+)<\/h1>/i',
                'name' => 'generic_h1',
                'priority' => 2
            ],
            [
                'pattern' => '/<meta[^>]*property=["\']og:title["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i',
                'name' => 'og_title',
                'priority' => 3
            ]
        ];
        
        foreach ($patterns as $pattern_info) {
            if (preg_match($pattern_info['pattern'], $html, $matches)) {
                $title = trim(strip_tags($matches[1]));
                $title = preg_replace('/^ดูหนัง\s*/i', '', $title);
                $title = preg_replace('/\s*เต็มเรื่อง.*$/i', '', $title);
                
                if (!empty($title) && strlen($title) > 3 &&
                    strpos($title, 'เว็บดูหนัง') === false && 
                    strpos($title, 'Netflix ฟรี') === false &&
                    strpos($title, 'ดูหนังใหม่') === false) {
                    
                    $this->log("✅ Title extracted: '$title' using pattern: {$pattern_info['name']}");
                    
                    if ($expectedTitle) {
                        $match = ($title === $expectedTitle) ? "✅ MATCH" : "❌ MISMATCH";
                        $this->log("   Expected: '$expectedTitle' - $match");
                    }
                    
                    return ['success' => true, 'value' => $title, 'pattern' => $pattern_info['name']];
                }
            }
        }
        
        $this->log("❌ Title extraction failed");
        return ['success' => false, 'value' => null, 'pattern' => null];
    }
    
    public function testIMDbRating($html, $expectedRating = null) {
        $this->log("⭐ Testing IMDb Rating Extraction...");
        
        $patterns = [
            [
                'pattern' => '/<span[^>]*class=["\']*score["\']*[^>]*>[\s\S]*?<\/noscript>([0-9]+\.?[0-9]*)/i',
                'name' => 'score_noscript',
                'priority' => 1
            ],
            [
                'pattern' => '/<span[^>]*class=["\']*score["\']*[^>]*>[\s\S]*?([0-9]+\.?[0-9]*)/i',
                'name' => 'score_span',
                'priority' => 2
            ]
        ];
        
        foreach ($patterns as $pattern_info) {
            if (preg_match($pattern_info['pattern'], $html, $matches)) {
                $rating = floatval($matches[1]);
                if ($rating >= 0 && $rating <= 10) {
                    $this->log("✅ IMDb rating extracted: $rating using pattern: {$pattern_info['name']}");
                    
                    if ($expectedRating) {
                        $match = (abs($rating - $expectedRating) < 0.1) ? "✅ MATCH" : "❌ MISMATCH";
                        $this->log("   Expected: $expectedRating - $match");
                    }
                    
                    return ['success' => true, 'value' => $rating, 'pattern' => $pattern_info['name']];
                }
            }
        }
        
        $this->log("❌ IMDb rating extraction failed");
        return ['success' => false, 'value' => null, 'pattern' => null];
    }
    
    public function testYouTubeID($html, $expectedId = null) {
        $this->log("🎬 Testing YouTube ID Extraction...");
        
        $patterns = [
            [
                'pattern' => '/videoId:\s*["\']([a-zA-Z0-9_-]+)["\']/',
                'name' => 'videoId_js',
                'priority' => 1
            ],
            [
                'pattern' => '/youtube\.com\/vi\/([a-zA-Z0-9_-]+)/',
                'name' => 'youtube_vi',
                'priority' => 2
            ]
        ];
        
        foreach ($patterns as $pattern_info) {
            if (preg_match($pattern_info['pattern'], $html, $matches)) {
                $youtube_id = $matches[1];
                if (preg_match('/^[a-zA-Z0-9_-]{10,12}$/', $youtube_id)) {
                    $this->log("✅ YouTube ID extracted: $youtube_id using pattern: {$pattern_info['name']}");
                    
                    if ($expectedId) {
                        $match = ($youtube_id === $expectedId) ? "✅ MATCH" : "❌ MISMATCH";
                        $this->log("   Expected: $expectedId - $match");
                    }
                    
                    return ['success' => true, 'value' => $youtube_id, 'pattern' => $pattern_info['name']];
                }
            }
        }
        
        $this->log("❌ YouTube ID extraction failed");
        return ['success' => false, 'value' => null, 'pattern' => null];
    }
    
    public function testPlayerButtons($html) {
        $this->log("🎮 Testing Player Button Extraction...");
        
        $pattern = '/<span[^>]+class=["\']*halim-btn[^"\']*["\']*[^>]+data-post-id=["\']*([^"\']+)["\']*[^>]+data-server=["\']*([^"\']+)["\']*[^>]*>/i';
        $matches = [];
        
        if (preg_match_all($pattern, $html, $allMatches, PREG_SET_ORDER)) {
            foreach ($allMatches as $match) {
                $matches[] = [
                    'postId' => $match[1],
                    'server' => $match[2]
                ];
            }
            
            $this->log("✅ Found " . count($matches) . " player buttons");
            foreach ($matches as $i => $btn) {
                $this->log("   " . ($i + 1) . ". Post ID: {$btn['postId']}, Server: {$btn['server']}");
            }
            
            return ['success' => true, 'value' => $matches, 'pattern' => 'halim_btn'];
        }
        
        $this->log("❌ Player button extraction failed");
        return ['success' => false, 'value' => [], 'pattern' => null];
    }
    
    public function testPosterImage($html) {
        $this->log("🖼️ Testing Poster Image Extraction...");
        
        $pattern = '/<meta[^>]*property=["\']*og:image["\']*[^>]*content=["\']*([^"\']+)["\']*[^>]*>/i';
        
        if (preg_match($pattern, $html, $matches)) {
            $posterUrl = trim($matches[1]);
            $this->log("✅ Poster image extracted: $posterUrl");
            return ['success' => true, 'value' => $posterUrl, 'pattern' => 'og_image'];
        }
        
        $this->log("❌ Poster image extraction failed");
        return ['success' => false, 'value' => null, 'pattern' => null];
    }
    
    public function runComprehensiveTest($htmlFile, $movieName, $expectedData = []) {
        $this->log("🎬 Testing: $movieName");
        $this->log("📁 HTML File: $htmlFile");
        $this->log(str_repeat("-", 60));
        
        if (!file_exists($htmlFile)) {
            $this->log("❌ HTML file not found: $htmlFile");
            return ['success' => false, 'error' => 'File not found'];
        }
        
        $html = file_get_contents($htmlFile);
        $this->log("📄 HTML loaded: " . strlen($html) . " characters");
        
        $results = [
            'movie' => $movieName,
            'htmlFile' => $htmlFile,
            'timestamp' => date('Y-m-d H:i:s'),
            'tests' => [],
            'summary' => ['passed' => 0, 'failed' => 0, 'total' => 5]
        ];
        
        // Test all patterns
        $results['tests']['title'] = $this->testTitleExtraction($html, $expectedData['title'] ?? null);
        $results['tests']['rating'] = $this->testIMDbRating($html, $expectedData['rating'] ?? null);
        $results['tests']['youtubeId'] = $this->testYouTubeID($html, $expectedData['youtubeId'] ?? null);
        $results['tests']['playerButtons'] = $this->testPlayerButtons($html);
        $results['tests']['posterImage'] = $this->testPosterImage($html);
        
        // Calculate summary
        foreach ($results['tests'] as $test) {
            if ($test['success']) {
                $results['summary']['passed']++;
            } else {
                $results['summary']['failed']++;
            }
        }
        
        $successRate = round(($results['summary']['passed'] / $results['summary']['total']) * 100);
        $results['summary']['successRate'] = $successRate;
        $results['success'] = $successRate >= 80; // 80% success rate required
        
        $this->log("📊 Test Summary:");
        $this->log("   Passed: {$results['summary']['passed']}/{$results['summary']['total']}");
        $this->log("   Success Rate: {$successRate}%");
        $this->log("   Overall: " . ($results['success'] ? "✅ PASS" : "❌ FAIL"));
        
        $this->testResults[] = $results;
        return $results;
    }
    
    public function generateReport() {
        $this->log("\n📊 FINAL PATTERN TEST REPORT");
        $this->log(str_repeat("=", 80));
        
        $totalTests = count($this->testResults);
        $passedTests = array_filter($this->testResults, function($result) { return $result['success']; });
        $passedCount = count($passedTests);
        
        $this->log("Total movies tested: $totalTests");
        $this->log("Passed tests: $passedCount");
        $this->log("Failed tests: " . ($totalTests - $passedCount));
        
        if ($totalTests > 0) {
            $overallSuccessRate = round(($passedCount / $totalTests) * 100);
            $this->log("Overall success rate: {$overallSuccessRate}%");
            
            // Detailed breakdown
            $this->log("\nDetailed Results:");
            foreach ($this->testResults as $i => $result) {
                $status = $result['success'] ? "✅ PASS" : "❌ FAIL";
                $this->log("   " . ($i + 1) . ". {$result['movie']}: {$result['summary']['successRate']}% - $status");
            }
            
            return $overallSuccessRate >= 80;
        }
        
        return false;
    }
}

// Run tests with saved HTML files
$tester = new PatternTester();

$testCases = [
    [
        'htmlFile' => '24hd_comprehensive_analysis/main_page.html',
        'movieName' => 'Latest Movie (from comprehensive analysis)',
        'expected' => []
    ]
];

// Check if we have the HTML file from previous analysis
if (file_exists('24hd_comprehensive_analysis/main_page.html')) {
    $testCases[0]['expected'] = [
        'title' => '11 Rebels (2024)', // Based on previous analysis
        'rating' => 6.3,
        'youtubeId' => '7-nUeY5z2Fs'
    ];
}

foreach ($testCases as $i => $testCase) {
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "TEST CASE " . ($i + 1) . "\n";
    echo str_repeat("=", 80) . "\n";
    
    $result = $tester->runComprehensiveTest(
        $testCase['htmlFile'], 
        $testCase['movieName'], 
        $testCase['expected']
    );
    
    if ($i < count($testCases) - 1) {
        echo "\n⏳ Waiting 1 second before next test...\n";
        sleep(1);
    }
}

echo "\n" . str_repeat("=", 80) . "\n";
$success = $tester->generateReport();

if ($success) {
    echo "\n🎉 ALL PATTERNS WORKING CORRECTLY! Ready for production.\n";
} else {
    echo "\n⚠️ Some patterns need adjustment before production.\n";
}

echo "\n✅ Pattern testing completed!\n";
?>
